{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-39:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3927d342be7108a61372589dd032352d\\transformed\\core-1.13.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "4,5,6,7,8,9,10,33", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "280,375,477,575,674,782,887,3625", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "370,472,570,669,777,882,1003,3721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\26e1d6f843b8bd9c55fbe9cde85475ac\\transformed\\browser-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "3214,3314,3414,3527", "endColumns": "99,99,112,97", "endOffsets": "3309,3409,3522,3620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\403f29e94df891a2dd1d4561277e8087\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2002", "endColumns": "147", "endOffsets": "2145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\467f116ae41acb0fa99753049275ea75\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,112", "endOffsets": "162,275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed293fa1fecc38363e0267349aa1d19f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1008,1115,1272,1399,1509,1650,1775,1898,2150,2298,2406,2568,2696,2850,3006,3072,3135", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "1110,1267,1394,1504,1645,1770,1893,1997,2293,2401,2563,2691,2845,3001,3067,3130,3209"}}]}]}