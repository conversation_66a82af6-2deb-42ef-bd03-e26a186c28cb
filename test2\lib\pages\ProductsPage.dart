import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/ProductProvider.dart';
import '../models/Product.dart';
import '../widgets/ProductGridCard.dart';
import '../widgets/LoadingWidget.dart';
import '../widgets/EmptyStateWidget.dart';

/// صفحة عرض المنتجات
class ProductsPage extends StatefulWidget {
  final String? storeId;
  final String? categoryId;
  final String? title;

  const ProductsPage({
    Key? key,
    this.storeId,
    this.categoryId,
    this.title,
  }) : super(key: key);

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedSortOption = 'الأحدث';
  
  final List<String> _sortOptions = [
    'الأحدث',
    'الأقدم',
    'السعر: من الأقل للأعلى',
    'السعر: من الأعلى للأقل',
    'الأكثر مشاهدة',
    'الاسم: أ-ي',
    'الاسم: ي-أ',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProducts();
    });
  }

  void _loadProducts() {
    final productProvider = Provider.of<ProductProvider>(context, listen: false);
    
    if (widget.storeId != null) {
      productProvider.loadProductsByStore(widget.storeId!);
    } else if (widget.categoryId != null) {
      productProvider.loadProductsByCategory(widget.categoryId!);
    } else {
      productProvider.loadAllProducts();
    }
  }

  void _onSearchChanged(String value) {
    final productProvider = Provider.of<ProductProvider>(context, listen: false);
    productProvider.searchProducts(value);
  }

  List<Product> _sortProducts(List<Product> products) {
    final sortedProducts = List<Product>.from(products);
    
    switch (_selectedSortOption) {
      case 'الأحدث':
        sortedProducts.sort((a, b) => (b.createdAt ?? DateTime.now()).compareTo(a.createdAt ?? DateTime.now()));
        break;
      case 'الأقدم':
        sortedProducts.sort((a, b) => (a.createdAt ?? DateTime.now()).compareTo(b.createdAt ?? DateTime.now()));
        break;
      case 'السعر: من الأقل للأعلى':
        sortedProducts.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'السعر: من الأعلى للأقل':
        sortedProducts.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'الأكثر مشاهدة':
        sortedProducts.sort((a, b) => b.orderCount.compareTo(a.orderCount));
        break;
      case 'الاسم: أ-ي':
        sortedProducts.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'الاسم: ي-أ':
        sortedProducts.sort((a, b) => b.name.compareTo(a.name));
        break;
    }
    
    return sortedProducts;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title ?? 'المنتجات',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1E88E5),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadProducts();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'ابحث عن المنتجات...',
                    prefixIcon: const Icon(Icons.search, color: Color(0xFF1E88E5)),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // خيارات الترتيب
                Row(
                  children: [
                    const Icon(Icons.sort, color: Color(0xFF1E88E5)),
                    const SizedBox(width: 8),
                    const Text(
                      'ترتيب حسب:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: DropdownButton<String>(
                        value: _selectedSortOption,
                        isExpanded: true,
                        underline: Container(),
                        items: _sortOptions.map((option) {
                          return DropdownMenuItem<String>(
                            value: option,
                            child: Text(option),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedSortOption = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // قائمة المنتجات
          Expanded(
            child: Consumer<ProductProvider>(
              builder: (context, productProvider, child) {
                if (productProvider.isLoading) {
                  return const LoadingWidget(message: 'جاري تحميل المنتجات...');
                }

                if (productProvider.errorMessage.isNotEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          productProvider.errorMessage,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.red[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _loadProducts,
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1E88E5),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final products = _sortProducts(productProvider.filteredProducts);

                if (products.isEmpty) {
                  return EmptyStateWidget(
                    icon: Icons.inventory_2_outlined,
                    title: 'لا توجد منتجات',
                    subtitle: _searchController.text.isNotEmpty
                        ? 'لم يتم العثور على منتجات تطابق البحث'
                        : 'لا توجد منتجات متاحة حالياً',
                    actionText: 'إعادة تحميل',
                    onActionPressed: _loadProducts,
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    _loadProducts();
                  },
                  child: GridView.builder(
                    padding: const EdgeInsets.all(16),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    itemCount: products.length,
                    itemBuilder: (context, index) {
                      final product = products[index];
                      return ProductGridCard(
                        product: product,
                        onTap: () {
                          // تحديث عدد المشاهدات
                          productProvider.incrementProductViews(product.id);
                          
                          // الانتقال لصفحة تفاصيل المنتج
                          Navigator.pushNamed(
                            context,
                            '/product-details',
                            arguments: product,
                          );
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
