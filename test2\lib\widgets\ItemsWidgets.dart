import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/DataManager.dart';
import '../utils/SearchManager.dart';
import '../widgets/OptimizedImage.dart';
import '../pages/StoreDetailsPage.dart';
import '../pages/ProductDetailsPage.dart';
import '../providers/CartProvider.dart';
import '../providers/StoresProvider.dart';
import '../providers/ProductProvider.dart';
import '../providers/FavoritesProvider.dart';
import '../models/Product.dart';
import '../models/Store.dart';
import '../widgets/CustomSnackBars.dart';

class ItemsWidgets extends StatelessWidget {
  final String selectedCategory;
  final String? searchQuery;
  final String? selectedTab;

  const ItemsWidgets({
    Key? key,
    required this.selectedCategory,
    this.searchQuery,
    this.selectedTab,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer2<StoresProvider, ProductProvider>(
      builder: (context, storesProvider, productProvider, child) {
        // استخدام SearchManager أو Firebase Providers حسب وجود البحث
        List<Map<String, dynamic>> allResults = [];

        if (searchQuery != null && searchQuery!.isNotEmpty) {
          // البحث العام - الحصول على المنتجات والمتاجر من Firebase

          // البحث في المنتجات من Firebase
          final firebaseProducts = productProvider.filteredProducts
              .where((product) =>
                  product.name.toLowerCase().contains(searchQuery!.toLowerCase()) ||
                  product.description.toLowerCase().contains(searchQuery!.toLowerCase()))
              .map((product) => _productToMap(product))
              .toList();

          // البحث في المحلات من Firebase
          final firebaseStores = storesProvider.allStores
              .where((store) =>
                  store.name.toLowerCase().contains(searchQuery!.toLowerCase()) ||
                  store.category.toLowerCase().contains(searchQuery!.toLowerCase()) ||
                  store.description.toLowerCase().contains(searchQuery!.toLowerCase()))
              .map((store) => DataManager.storeToMap(store))
              .toList();

          // دمج المنتجات والمتاجر مع إضافة نوع لكل عنصر
          allResults = [];

          // إضافة المنتجات
          for (var item in firebaseProducts) {
            allResults.add({...item, 'type': 'item'});
          }

          // إضافة المتاجر
          for (var store in firebaseStores) {
            allResults.add({...store, 'type': 'store'});
          }
        } else {
          // العرض العادي - المنتجات والمتاجر من Firebase حسب التبويب

          // الحصول على المنتجات من Firebase
          List<Product> firebaseProducts = [];
          if (selectedCategory == 'الكل') {
            firebaseProducts = productProvider.allProducts;
          } else {
            firebaseProducts = productProvider.allProducts
                .where((product) => product.category == selectedCategory)
                .toList();
          }

          // الحصول على المحلات من Firebase
          List<Store> firebaseStores = [];
          if (selectedCategory == 'الكل') {
            firebaseStores = storesProvider.allStores;
          } else {
            firebaseStores = storesProvider.allStores
                .where((store) => store.category == selectedCategory)
                .toList();
          }

          // فلترة حسب التبويب المختار
          List<Product> filteredProducts = [];
          List<Store> filteredStores = [];

          switch (selectedTab) {
            case "الكل":
              filteredProducts = firebaseProducts;
              filteredStores = firebaseStores;
              break;
            case "الأقرب":
              // فلترة المتاجر الأقرب (يمكن تطويرها لاحقاً بناءً على الموقع)
              filteredProducts = firebaseProducts.take(6).toList();
              filteredStores = firebaseStores.take(3).toList();
              break;
            case "الجديد":
              // فلترة المنتجات والمتاجر الجديدة
              filteredProducts = productProvider.latestProducts.take(6).toList();
              filteredStores = storesProvider.newStores.take(2).toList();
              break;
            case "المفضلة":
              // فلترة المفضلة
              filteredProducts = productProvider.featuredProducts.take(6).toList();
              filteredStores = storesProvider.featuredStores.take(3).toList();
              break;
            default:
              filteredProducts = firebaseProducts;
              filteredStores = firebaseStores;
          }

          // تحويل المنتجات والمحلات إلى Map
          final productsAsMap = filteredProducts
              .map((product) => _productToMap(product))
              .toList();

          final storesAsMap = filteredStores
              .map((store) => DataManager.storeToMap(store))
              .toList();

          // دمج المنتجات والمتاجر
          allResults = [];

          // إضافة المنتجات
          for (var item in productsAsMap) {
            allResults.add({...item, 'type': 'item'});
          }

          // إضافة المتاجر
          for (var store in storesAsMap) {
            allResults.add({...store, 'type': 'store'});
          }
        }

        return GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.85,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: allResults.length,
          itemBuilder: (context, index) {
            final item = allResults[index];
            final isStore = item['type'] == 'store';

            return isStore
                ? StoreCard(
                    key: ValueKey('store_${item["name"]}'),
                    store: item,
                  )
                : ItemCard(
                    key: ValueKey('item_${item["id"]}'),
                    item: item,
                  );
          },
        );
      },
    );
  }

  /// تحويل Product إلى Map
  Map<String, dynamic> _productToMap(Product product) {
    return {
      'id': product.id,
      'name': product.name,
      'description': product.description,
      'price': product.price,
      'imageUrl': product.imageUrl,
      'category': product.category,
      'storeId': product.storeId,
      'storeName': product.storeName,
      'rating': product.rating,
      'reviewCount': product.reviewCount,
      'isAvailable': product.isAvailable,
      'isFeatured': product.isFeatured,
      'discountPercentage': product.discountPercentage,
    };
  }
}

// مكون منفصل للبطاقة لتحسين الأداء
class ItemCard extends StatelessWidget {
  final Map<String, dynamic> item;

  const ItemCard({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصف العلوي - الخصم والمفضلة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 245, 170, 73),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  "-50%",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Consumer<FavoritesProvider>(
                builder: (context, favoritesProvider, child) {
                  final isFavorite = favoritesProvider.isProductFavorite(item["id"].toString());
                  return GestureDetector(
                    onTap: () => _toggleProductFavorite(context, item["id"].toString()),
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.grey,
                    ),
                  );
                },
              )
            ],
          ),

          // صورة المنتج
          Container(
            height: 80,
            child: InkWell(
              onTap: () => _navigateToProductDetails(context),
              child: Container(
                margin: const EdgeInsets.all(10),
                child: OptimizedImage(
                  imagePath: item["imageUrl"] ?? "images/${item["id"]}.png",
                  width: 50,
                  height: 50,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),

          // معلومات المنتج
          InkWell(
            onTap: () => _navigateToProductDetails(context),
            child: Container(
              padding: const EdgeInsets.only(bottom: 8),
              alignment: Alignment.centerRight,
              child: Text(
                item["name"]?.toString() ?? "منتج غير معروف",
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          InkWell(
            onTap: () => _navigateToProductDetails(context),
            child: Container(
              alignment: Alignment.centerRight,
              child: Text(
                item["description"]?.toString() ?? "وصف المنتج",
                style: const TextStyle(fontSize: 9, color: Colors.black),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // السعر وزر الإضافة
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () => _navigateToProductDetails(context),
                  child: Text(
                    "${item["price"]?.toString() ?? "0"} ر.ي",
                    style: const TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Consumer<CartProvider>(
                  builder: (context, cartProvider, child) {
                    final productId = item["id"]?.toString() ?? '';
                    final isInCart = cartProvider.isProductInCart(productId);
                    final quantity = cartProvider.getProductQuantity(productId);

                    return GestureDetector(
                      onTap: () => _addToCart(context, item, cartProvider),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: isInCart
                              ? Colors.green
                              : const Color.fromARGB(255, 245, 170, 73),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isInCart
                                  ? Icons.check
                                  : Icons.shopping_cart_checkout,
                              color: Colors.white,
                              size: 12,
                            ),
                            if (isInCart && quantity > 0) ...[
                              const SizedBox(width: 4),
                              Text(
                                quantity.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// إضافة المنتج إلى السلة
  void _addToCart(BuildContext context, Map<String, dynamic> item,
      CartProvider cartProvider) {
    try {
      // تحويل البيانات إلى Product مع إضافة الحقول المفقودة ومعلومات المتجر
      final productData = {
        'id': item['id']?.toString() ?? '',
        'name': item['name']?.toString() ?? '',
        'description': item['description']?.toString() ?? '',
        'price': _parsePrice(item['price']),
        'imageUrl': 'images/${item['id']}.png',
        'category': item['category']?.toString() ?? '',
        'storeId': item['storeId']?.toString() ?? '',
        'storeName': item['storeName']?.toString() ?? 'متجر غير محدد',
        'rating': 4.5,
        'reviewCount': 10,
        'isAvailable': true,
        'isFeatured': false,
      };

      final product = Product.fromMap(productData);

      // إضافة المنتج إلى السلة
      cartProvider.addToCart(product).then((success) {
        if (success) {
          // عرض رسالة نجاح
          CustomSnackBars.showSuccess(
            context,
            message: 'تم إضافة ${product.name} إلى السلة',
            subtitle: 'يمكنك مراجعة سلتك من الأسفل',
          );
        } else {
          // عرض رسالة خطأ
          CustomSnackBars.showError(
            context,
            message: 'فشل في إضافة المنتج',
            subtitle: 'يرجى المحاولة مرة أخرى',
          );
        }
      });
    } catch (e) {
      // عرض رسالة خطأ
      CustomSnackBars.showError(
        context,
        message: 'خطأ في إضافة المنتج',
        subtitle: e.toString(),
      );
    }
  }

  /// الانتقال لصفحة تفاصيل المنتج
  void _navigateToProductDetails(BuildContext context) {
    // تحويل البيانات إلى Product model
    final productData = {
      'id': item['id']?.toString() ?? '',
      'name': item['name']?.toString() ?? '',
      'description': item['description']?.toString() ?? '',
      'price': _parsePrice(item['price']),
      'imageUrl': item['imageUrl']?.toString() ?? 'images/${item['id']}.png',
      'category': item['category']?.toString() ?? '',
      'storeId': item['storeId']?.toString() ?? '',
      'storeName': item['storeName']?.toString() ?? 'متجر غير محدد',
      'rating': 4.5,
      'reviewCount': 10,
      'isAvailable': true,
      'isFeatured': false,
      'discountPercentage': _parsePrice(item['discountPercentage']),
    };

    final product = Product.fromMap(productData);

    // الانتقال لصفحة تفاصيل المنتج
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsPage(product: product),
      ),
    );
  }

  /// تحويل السعر من String إلى double بشكل آمن
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) {
      if (price.isNaN || price.isInfinite) return 0.0;
      return price;
    }
    if (price is int) return price.toDouble();
    if (price is String) {
      final parsed = double.tryParse(price) ?? 0.0;
      if (parsed.isNaN || parsed.isInfinite) return 0.0;
      return parsed;
    }
    return 0.0;
  }

  /// تبديل حالة المفضلة للمنتج
  void _toggleProductFavorite(BuildContext context, String productId) {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);

    favoritesProvider.toggleProductFavorite(productId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isProductFavorite(productId);
        CustomSnackBars.showSuccess(
          context,
          message: isFavorite ? 'تم إضافة المنتج للمفضلة' : 'تم إزالة المنتج من المفضلة',
          subtitle: isFavorite ? 'يمكنك العثور عليه في صفحة المفضلات' : '',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في تحديث المفضلة',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }
}

// مكون منفصل لبطاقة المتجر
class StoreCard extends StatelessWidget {
  final Map<String, dynamic> store;

  const StoreCard({
    Key? key,
    required this.store,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StoreDetailsPage(store: store),
            ),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFF4C53A5).withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // الصف العلوي - نوع المتجر وحالة المحل والمفضلة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // نوع المتجر
                      Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4C53A5),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          "متجر",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // حالة المحل (مفتوح/مغلق)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: (store["isOpen"] ?? false)
                              ? Colors.green // أخضر للمفتوح
                              : Colors.red, // أحمر للمغلق
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Text(
                          (store["isOpen"] ?? false) ? "مفتوح" : "مغلق",
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white, // نص أبيض
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Consumer<FavoritesProvider>(
                    builder: (context, favoritesProvider, child) {
                      final storeId = store["id"]?.toString() ?? '';
                      final isFavorite = favoritesProvider.isStoreFavorite(storeId);
                      return GestureDetector(
                        onTap: () => _toggleStoreFavorite(context, storeId),
                        child: Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: isFavorite ? Colors.red : Colors.grey,
                        ),
                      );
                    },
                  )
                ],
              ),

              // صورة المتجر
              Container(
                height: 80,
                child: Container(
                  margin: const EdgeInsets.all(10),
                  child: OptimizedImage(
                    imagePath: store["image"] ?? "images/1.png",
                    width: 50,
                    height: 50,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // معلومات المتجر
              Container(
                padding: const EdgeInsets.only(bottom: 8),
                alignment: Alignment.centerRight,
                child: Text(
                  store["store_name"]?.toString() ??
                  store["name"]?.toString() ??
                  "متجر غير معروف",
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  store["store_categ"]?.toString() ??
                  store["category"]?.toString() ??
                  "عام",
                  style: const TextStyle(fontSize: 11, color: Colors.black),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // التقييم ووقت التوصيل
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.orange,
                          size: 12,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          "${store["rating"] ?? "4.5"}",
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    // Text(
                    //   store["deliveryTime"]?.toString() ?? "30 دقيقة",
                    //   style: const TextStyle(
                    //     fontSize: 10,
                    //     color: Colors.grey,
                    //   ),
                    // ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  /// تبديل حالة المفضلة للمحل
  void _toggleStoreFavorite(BuildContext context, String storeId) {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);

    favoritesProvider.toggleStoreFavorite(storeId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isStoreFavorite(storeId);
        CustomSnackBars.showSuccess(
          context,
          message: isFavorite ? 'تم إضافة المحل للمفضلة' : 'تم إزالة المحل من المفضلة',
          subtitle: isFavorite ? 'يمكنك العثور عليه في صفحة المفضلات' : '',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في تحديث المفضلة',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }
}
