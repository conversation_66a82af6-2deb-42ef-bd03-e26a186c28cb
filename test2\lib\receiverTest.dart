import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(RestaurantApp());
}

class RestaurantApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مطعم الشيباني',
      home: OrdersListenerScreen(),
    );
  }
}

class OrdersListenerScreen extends StatefulWidget {
  @override
  _OrdersListenerScreenState createState() => _OrdersListenerScreenState();
}

class _OrdersListenerScreenState extends State<OrdersListenerScreen> {
  final DatabaseReference ref = FirebaseDatabase.instance.ref("orders/rest_alshaybani");
  List<Map<dynamic, dynamic>> orders = [];

  @override
  void initState() {
    super.initState();

    ref.onChildAdded.listen((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>;
      setState(() {
        orders.add(data);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("طلبات الشيباني")),
      body: ListView.builder(
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return ListTile(
            title: Text("طلب من: ${order['clientId']}"),
            subtitle: Text("المنتجات: ${order['items'].join(', ')}"),
            trailing: Text(order['status']),
          );
        },
      ),
    );
  }
}
