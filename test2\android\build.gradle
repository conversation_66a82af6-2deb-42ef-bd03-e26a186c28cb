allprojects {
    repositories {
        google()
        mavenCentral()
    }

}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // classpath 'com.android.tools.build:gradle:8.0.2' // أو حسب إصدارك
        classpath 'com.google.gms:google-services:4.4.2'
    }
}