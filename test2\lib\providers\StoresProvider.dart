import 'package:flutter/material.dart';
import 'dart:async';
import '../models/Store.dart';
import '../services/FirebaseStoresService.dart';
import '../services/NotificationService.dart';

/// مزود حالة المحلات مع التزامن مع Firebase
class StoresProvider extends ChangeNotifier {
  final FirebaseStoresService _storesService = FirebaseStoresService();
  final NotificationService _notificationService = NotificationService();

  List<Store> _allStores = [];
  List<Store> _filteredStores = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String? _error;
  
  // للإشعارات
  StreamSubscription<List<Store>>? _storesSubscription;
  List<String> _knownStoreIds = [];

  // Getters
  List<Store> get allStores => _allStores;
  List<Store> get filteredStores => _filteredStores;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// تهيئة المزود
  Future<void> initialize() async {
    await loadStores();
    _startListeningToStores();
  }

  /// تحميل المحلات من Firebase
  Future<void> loadStores() async {
    _setLoading(true);
    _clearError();

    try {
      _allStores = await _storesService.getAllStores();
      _knownStoreIds = _allStores.map((store) => store.id).toList();
      _applyFilter();
      
      print('تم تحميل ${_allStores.length} محل من Firebase');
    } catch (e) {
      _setError('فشل في تحميل المحلات: ${e.toString()}');
      print('خطأ في تحميل المحلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// بدء مراقبة التغييرات في الوقت الفعلي
  void _startListeningToStores() {
    
    _storesSubscription = _storesService.watchStores().listen(
      (stores) {
        
        _handleStoresUpdate(stores);
      },
      onError: (error) {
        _setError('خطأ في مراقبة المحلات: ${error.toString()}');
        print('خطأ في مراقبة المحلات: $error');
      },
    );
  }

  /// معالجة تحديث المحلات
  void _handleStoresUpdate(List<Store> newStores) {
    // البحث عن المحلات الجديدة
    final newStoreIds = newStores.map((store) => store.id).toList();
    final addedStoreIds = newStoreIds.where((id) => !_knownStoreIds.contains(id)).toList();
    
    // إرسال إشعارات للمحلات الجديدة
    if (addedStoreIds.isNotEmpty) {
      for (String storeId in addedStoreIds) {
        final newStore = newStores.firstWhere((store) => store.id == storeId);
        _sendNewStoreNotification(newStore);
      }
    }

    // تحديث البيانات
    _allStores = newStores;
    _knownStoreIds = newStoreIds;
    _applyFilter();
    
    print('تم تحديث المحلات: ${_allStores.length} محل');
  }

  /// إرسال إشعار للمحل الجديد
  void _sendNewStoreNotification(Store store) {
    _notificationService.showNewStoreNotification(
      storeName: store.name,
      storeCategory: store.category,
    );

    print('تم إرسال إشعار للمحل الجديد: ${store.name}');
  }

  /// تطبيق الفلتر على المحلات
  void _applyFilter() {
    if (_selectedCategory == 'الكل') {
      _filteredStores = List.from(_allStores);
    } else {
      _filteredStores = _allStores
          .where((store) => store.category == _selectedCategory)
          .toList();
    }
    notifyListeners();
  }

  /// تغيير الفئة المحددة
  void setCategory(String category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilter();
    }
  }

  /// البحث في المحلات
  Future<List<Store>> searchStores(String searchTerm) async {
    if (searchTerm.isEmpty) {
      return _filteredStores;
    }

    try {
      // البحث في Firebase أولاً
      final firebaseResults = await _storesService.searchStores(searchTerm);
      
      // البحث المحلي كبديل
      final localResults = _allStores.where((store) {
        return store.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
               store.category.toLowerCase().contains(searchTerm.toLowerCase()) ||
               (store.description.toLowerCase().contains(searchTerm.toLowerCase()));
      }).toList();

      // دمج النتائج وإزالة التكرار
      final Set<String> seenIds = {};
      final List<Store> combinedResults = [];

      for (var store in [...firebaseResults, ...localResults]) {
        if (!seenIds.contains(store.id)) {
          seenIds.add(store.id);
          combinedResults.add(store);
        }
      }

      return combinedResults;
    } catch (e) {
      print('خطأ في البحث: $e');
      // البحث المحلي فقط في حالة الخطأ
      return _allStores.where((store) {
        return store.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
               store.category.toLowerCase().contains(searchTerm.toLowerCase());
      }).toList();
    }
  }

  /// الحصول على محل بالمعرف
  Store? getStoreById(String storeId) {
    try {
      return _allStores.firstWhere((store) => store.id == storeId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المحلات المفتوحة فقط
  List<Store> get openStores {
    return _filteredStores.where((store) => store.isOpen).toList();
  }

  /// الحصول على المحلات المغلقة فقط
  List<Store> get closedStores {
    return _filteredStores.where((store) => !store.isOpen).toList();
  }

  /// الحصول على المحلات المميزة
  List<Store> get featuredStores {
    return _allStores.where((store) => store.isFeatured).toList();
  }

  /// الحصول على المحلات الجديدة (أقل من 24 ساعة)
  List<Store> get newStores {
    final now = DateTime.now();
    return _allStores.where((store) {
      if (store.createdAt == null) return false;
      final difference = now.difference(store.createdAt!);
      return difference.inHours < 24;
    }).toList();
  }

  /// تحديث حالة محل (مفتوح/مغلق)
  Future<bool> updateStoreStatus(String storeId, bool isOpen) async {
    try {
      final success = await _storesService.updateStoreStatus(storeId, isOpen);
      if (success) {
        // تحديث البيانات المحلية
        final storeIndex = _allStores.indexWhere((store) => store.id == storeId);
        if (storeIndex != -1) {
          _allStores[storeIndex] = _allStores[storeIndex].copyWith(
            isOpen: isOpen,
            updatedAt: DateTime.now(),
          );
          _applyFilter();
        }
      }
      return success;
    } catch (e) {
      _setError('فشل في تحديث حالة المحل: ${e.toString()}');
      return false;
    }
  }

  /// إضافة محل جديد (للاختبار)
  Future<bool> addStore(Store store) async {
    try {
      final success = await _storesService.addStore(store);
      if (success) {
        // سيتم تحديث البيانات تلقائياً عبر الـ Stream
        print('تم إضافة المحل بنجاح: ${store.name}');
      }
      return success;
    } catch (e) {
      _setError('فشل في إضافة المحل: ${e.toString()}');
      return false;
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await loadStores();
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    _storesSubscription?.cancel();
    super.dispose();
  }

  // دوال مساعدة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// إضافة بيانات تجريبية
  Future<void> addSampleData() async {
    await _storesService.addSampleStores();
  }

  /// الحصول على إحصائيات المحلات
  Map<String, int> get storeStats {
    return {
      'total': _allStores.length,
      'open': _allStores.where((store) => store.isOpen).length,
      'closed': _allStores.where((store) => !store.isOpen).length,
      'featured': _allStores.where((store) => store.isFeatured).length,
      'verified': _allStores.where((store) => store.isVerified).length,
    };
  }

  /// الحصول على الفئات المتاحة
  List<String> get availableCategories {
    final categories = _allStores.map((store) => store.category).toSet().toList();
    categories.sort();
    return ['الكل', ...categories];
  }
}
