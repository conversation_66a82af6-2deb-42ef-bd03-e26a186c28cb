import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Order.dart';
import '../models/CartItem.dart';
import '../models/Address.dart';
import '../models/UserProfile.dart';
import 'CustomerDataService.dart';
import 'FirebaseOrderService.dart';

/// خدمة إدارة الطلبات
class OrderService {
  static const String _ordersKey = 'user_orders';
  static const String _orderCounterKey = 'order_counter';

  /// إنشاء طلب جديد باستخدام بيانات العميل من CustomerDataService
  static Future<Order?> createOrderFromCustomerData({
    required Address deliveryAddress,
    required List<CartItem> items,
    required String paymentMethod,
    required String deliveryTime,
    DateTime? scheduledTime,
    String? notes,
  }) async {
    try {
      // إنشاء الطلب وإرساله إلى Firebase
      final firebaseOrderService = FirebaseOrderService();
      final order = await firebaseOrderService.createOrderFromCustomerData(
        deliveryAddress: deliveryAddress,
        items: items,
        paymentMethod: paymentMethod,
        deliveryTime: deliveryTime,
        scheduledTime: scheduledTime,
        notes: notes,
      );

      if (order != null) {
        // حفظ الطلب محلياً أيضاً كنسخة احتياطية
        await _saveOrder(order);
        print('تم إنشاء الطلب وإرساله إلى Firebase بنجاح: ${order.id}');
        return order;
      } else {
        throw Exception('فشل في إنشاء الطلب في Firebase');
      }
    } catch (e) {
      print('خطأ في إنشاء الطلب: $e');

      // في حالة فشل Firebase، إنشاء الطلب محلياً فقط
      final customerService = CustomerDataService();
      await customerService.ensureDataLoaded();

      final userProfile = customerService.customerProfile;
      if (userProfile == null) {
        throw Exception('لم يتم العثور على بيانات العميل');
      }

      final localOrder = await createOrder(
        userProfile: userProfile,
        deliveryAddress: deliveryAddress,
        items: items,
        paymentMethod: paymentMethod,
        deliveryTime: deliveryTime,
        scheduledTime: scheduledTime,
        notes: notes,
      );

      print('تم إنشاء الطلب محلياً كنسخة احتياطية: ${localOrder.id}');
      return localOrder;
    }
  }

  /// إنشاء طلب جديد
  static Future<Order> createOrder({
    required UserProfile userProfile,
    required Address deliveryAddress,
    required List<CartItem> items,
    required String paymentMethod,
    required String deliveryTime,
    DateTime? scheduledTime,
    String? notes,
  }) async {
    try {
      // إنشاء معرف الطلب
      final orderId = await _generateOrderId();

      // حساب المبالغ
      final subtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
      final deliveryFee = _calculateDeliveryFee(subtotal);
      final tax = _calculateTax(subtotal);
      final discount = _calculateDiscount(subtotal);
      final total = subtotal + deliveryFee + tax - discount;

      // تحديد المحل (من أول عنصر في السلة)
      final storeInfo = _getStoreInfo(items.first.productId);

      // إنشاء الطلب
      final order = Order(
        id: orderId,
        userId: userProfile.id,
        userName: userProfile.fullName,
        userPhone: userProfile.phone,
        userEmail: userProfile.email,
        deliveryAddress: deliveryAddress,
        items: items,
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        tax: tax,
        discount: discount,
        total: total,
        status: OrderStatus.pending,
        paymentMethod: _parsePaymentMethod(paymentMethod),
        deliveryTime: _parseDeliveryTime(deliveryTime),
        scheduledTime: scheduledTime,
        notes: notes,
        storeId: storeInfo['id'],
        storeName: storeInfo['name'],
        estimatedDeliveryMinutes: _calculateEstimatedDelivery(),
        statusHistory: [
          OrderStatusUpdate(
            status: OrderStatus.pending,
            timestamp: DateTime.now(),
            message: 'تم إنشاء الطلب',
            updatedBy: userProfile.fullName,
            updatedByRole: 'customer',
          ),
        ],
      );

      // حفظ الطلب
      await _saveOrder(order);

      // بدء محاكاة تحديثات الحالة
      _simulateOrderUpdates(order);

      return order;
    } catch (e) {
      throw Exception('فشل في إنشاء الطلب: $e');
    }
  }

  /// الحصول على جميع طلبات المستخدم
  static Future<List<Order>> getUserOrders(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getStringList(_ordersKey) ?? [];

      final orders = ordersJson
          .map((json) => Order.fromMap(jsonDecode(json)))
          .where((order) => order.userId == userId)
          .toList();

      // ترتيب حسب التاريخ (الأحدث أولاً)
      orders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return orders;
    } catch (e) {
      print('خطأ في تحميل الطلبات: $e');
      return [];
    }
  }

  /// الحصول على طلب بالمعرف
  static Future<Order?> getOrderById(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getStringList(_ordersKey) ?? [];

      for (final json in ordersJson) {
        final order = Order.fromMap(jsonDecode(json));
        if (order.id == orderId) {
          return order;
        }
      }

      return null;
    } catch (e) {
      print('خطأ في تحميل الطلب: $e');
      return null;
    }
  }

  /// تحديث حالة الطلب
  static Future<bool> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    String? message,
    String? updatedBy,
    String? updatedByRole,
  }) async {
    try {
      final order = await getOrderById(orderId);
      if (order == null) return false;

      // إنشاء تحديث جديد للحالة
      final statusUpdate = OrderStatusUpdate(
        status: newStatus,
        timestamp: DateTime.now(),
        message: message ?? OrderStatusHelper.getStatusMessage(newStatus),
        updatedBy: updatedBy,
        updatedByRole: updatedByRole,
      );

      // تحديث الطلب
      final updatedOrder = order.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
        statusHistory: [...order.statusHistory, statusUpdate],
      );

      // حفظ التحديث
      await _updateOrder(updatedOrder);

      return true;
    } catch (e) {
      print('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// إلغاء الطلب
  static Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      // محاولة إلغاء الطلب في Firebase أولاً
      final firebaseOrderService = FirebaseOrderService();
      final firebaseSuccess = await firebaseOrderService.cancelOrder(orderId, reason);

      if (firebaseSuccess) {
        print('تم إلغاء الطلب في Firebase بنجاح: $orderId');
      } else {
        print('فشل في إلغاء الطلب في Firebase، سيتم الإلغاء محلياً فقط');
      }

      // إلغاء الطلب محلياً أيضاً
      final localSuccess = await updateOrderStatus(
        orderId: orderId,
        newStatus: OrderStatus.cancelled,
        message: 'تم إلغاء الطلب: $reason',
        updatedByRole: 'customer',
      );

      // إرجاع true إذا نجح الإلغاء في Firebase أو محلياً
      return firebaseSuccess || localSuccess;
    } catch (e) {
      print('خطأ في إلغاء الطلب: $e');

      // في حالة الخطأ، محاولة الإلغاء محلياً فقط
      return await updateOrderStatus(
        orderId: orderId,
        newStatus: OrderStatus.cancelled,
        message: 'تم إلغاء الطلب: $reason',
        updatedByRole: 'customer',
      );
    }
  }

  /// حفظ الطلب
  static Future<void> _saveOrder(Order order) async {
    final prefs = await SharedPreferences.getInstance();
    final ordersJson = prefs.getStringList(_ordersKey) ?? [];
    ordersJson.add(jsonEncode(order.toMap()));
    await prefs.setStringList(_ordersKey, ordersJson);
  }

  /// تحديث الطلب
  static Future<void> _updateOrder(Order updatedOrder) async {
    final prefs = await SharedPreferences.getInstance();
    final ordersJson = prefs.getStringList(_ordersKey) ?? [];

    // البحث عن الطلب وتحديثه
    for (int i = 0; i < ordersJson.length; i++) {
      final order = Order.fromMap(jsonDecode(ordersJson[i]));
      if (order.id == updatedOrder.id) {
        ordersJson[i] = jsonEncode(updatedOrder.toMap());
        break;
      }
    }

    await prefs.setStringList(_ordersKey, ordersJson);
  }

  /// توليد معرف طلب جديد
  static Future<String> _generateOrderId() async {
    final prefs = await SharedPreferences.getInstance();
    final counter = prefs.getInt(_orderCounterKey) ?? 1000;
    final newCounter = counter + 1;
    await prefs.setInt(_orderCounterKey, newCounter);
    return 'ORD$newCounter';
  }

  /// حساب رسوم التوصيل
  static double _calculateDeliveryFee(double subtotal) {
    if (subtotal >= 100) return 0.0; // توصيل مجاني للطلبات أكثر من 100 ريال
    return 15.0; // رسوم توصيل ثابتة
  }

  /// حساب الضريبة
  static double _calculateTax(double subtotal) {
    return subtotal * 0.15; // ضريبة القيمة المضافة 15%
  }

  /// حساب الخصم
  static double _calculateDiscount(double subtotal) {
    if (subtotal >= 200)
      return subtotal * 0.1; // خصم 10% للطلبات أكثر من 200 ريال
    return 0.0;
  }

  /// تحديد وقت التوصيل المقدر
  static double _calculateEstimatedDelivery() {
    return 30.0 + (DateTime.now().minute % 30); // 30-60 دقيقة
  }

  /// الحصول على معلومات المحل
  static Map<String, String> _getStoreInfo(String productId) {
    // استخراج معرف المحل من معرف المنتج
    final storeId = productId.split('_').first;

    final stores = {
      'store1': 'مطعم الأصالة',
      'store2': 'مقهى النخيل',
      'store3': 'مطعم البيت الشامي',
      'store4': 'مطعم الكرم',
      'store5': 'مقهى الورد',
    };

    return {
      'id': storeId,
      'name': stores[storeId] ?? 'متجر غير معروف',
    };
  }

  /// تحويل طريقة الدفع
  static PaymentMethod _parsePaymentMethod(String method) {
    switch (method) {
      case 'cash':
        return PaymentMethod.cash;
      case 'card':
        return PaymentMethod.card;
      case 'apple_pay':
        return PaymentMethod.applePay;
      case 'stc_pay':
        return PaymentMethod.stcPay;
      case 'mada':
        return PaymentMethod.mada;
      default:
        return PaymentMethod.cash;
    }
  }

  /// تحويل وقت التوصيل
  static DeliveryTime _parseDeliveryTime(String time) {
    switch (time) {
      case 'asap':
        return DeliveryTime.asap;
      case 'scheduled':
        return DeliveryTime.scheduled;
      default:
        return DeliveryTime.asap;
    }
  }

  /// محاكاة تحديثات الطلب
  static void _simulateOrderUpdates(Order order) {
    // محاكاة تحديثات الحالة بشكل تلقائي
    Timer(Duration(minutes: 2), () {
      updateOrderStatus(
        orderId: order.id,
        newStatus: OrderStatus.confirmed,
        message: 'تم تأكيد الطلب من ${order.storeName}',
        updatedBy: order.storeName,
        updatedByRole: 'store',
      );
    });

    Timer(Duration(minutes: 5), () {
      updateOrderStatus(
        orderId: order.id,
        newStatus: OrderStatus.preparing,
        message: 'بدء تجهيز الطلب',
        updatedBy: order.storeName,
        updatedByRole: 'store',
      );
    });

    Timer(Duration(minutes: 15), () {
      updateOrderStatus(
        orderId: order.id,
        newStatus: OrderStatus.ready,
        message: 'الطلب جاهز للاستلام',
        updatedBy: order.storeName,
        updatedByRole: 'store',
      );
    });

    Timer(Duration(minutes: 20), () {
      updateOrderStatus(
        orderId: order.id,
        newStatus: OrderStatus.pickedUp,
        message: 'تم استلام الطلب من عامل التوصيل',
        updatedBy: 'أحمد محمد - عامل التوصيل',
        updatedByRole: 'delivery',
      );
    });

    Timer(Duration(minutes: 25), () {
      updateOrderStatus(
        orderId: order.id,
        newStatus: OrderStatus.onTheWay,
        message: 'عامل التوصيل في الطريق إليك',
        updatedBy: 'أحمد محمد - عامل التوصيل',
        updatedByRole: 'delivery',
      );
    });

    Timer(Duration(minutes: 35), () {
      updateOrderStatus(
        orderId: order.id,
        newStatus: OrderStatus.delivered,
        message: 'تم توصيل الطلب بنجاح',
        updatedBy: 'أحمد محمد - عامل التوصيل',
        updatedByRole: 'delivery',
      );
    });
  }

  /// مسح جميع الطلبات (للتطوير)
  static Future<void> clearAllOrders() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_ordersKey);
    await prefs.remove(_orderCounterKey);
  }
}
