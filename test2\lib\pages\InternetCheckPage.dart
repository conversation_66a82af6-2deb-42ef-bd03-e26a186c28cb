import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import '../pages/SplashScreen.dart';

/// صفحة فحص الاتصال بالإنترنت
class InternetCheckPage extends StatefulWidget {
  const InternetCheckPage({Key? key}) : super(key: key);

  @override
  State<InternetCheckPage> createState() => _InternetCheckPageState();
}

class _InternetCheckPageState extends State<InternetCheckPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isConnected = false;
  bool _isChecking = true;
  String _connectionStatus = 'جاري فحص الاتصال...';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkInitialConnection();
    _startListeningToConnectivity();
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  /// فحص الاتصال الأولي
  Future<void> _checkInitialConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      _updateConnectionStatus(connectivityResult);
    } catch (e) {
      print('خطأ في فحص الاتصال: $e');
      setState(() {
        _isConnected = false;
        _isChecking = false;
        _connectionStatus = 'خطأ في فحص الاتصال';
      });
    }
  }

  /// بدء مراقبة حالة الاتصال
  void _startListeningToConnectivity() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      _updateConnectionStatus,
      onError: (error) {
        print('خطأ في مراقبة الاتصال: $error');
        setState(() {
          _isConnected = false;
          _isChecking = false;
          _connectionStatus = 'خطأ في مراقبة الاتصال';
        });
      },
    );
  }

  /// تحديث حالة الاتصال
  void _updateConnectionStatus(ConnectivityResult result) {
    final isConnected = result != ConnectivityResult.none;
    
    setState(() {
      _isConnected = isConnected;
      _isChecking = false;
      
      if (isConnected) {
        if (result == ConnectivityResult.wifi) {
          _connectionStatus = 'متصل عبر WiFi';
        } else if (result == ConnectivityResult.mobile) {
          _connectionStatus = 'متصل عبر بيانات الجوال';
        } else {
          _connectionStatus = 'متصل بالإنترنت';
        }
      } else {
        _connectionStatus = 'غير متصل بالإنترنت';
      }
    });

    // إذا كان متصل، الانتقال إلى الشاشة التالية بعد ثانيتين
    if (isConnected) {
      Timer(const Duration(seconds: 2), () {
        if (mounted) {
          _navigateToNextScreen();
        }
      });
    }
  }

  /// الانتقال إلى الشاشة التالية
  void _navigateToNextScreen() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => SplashScreen(),
      ),
    );
  }

  /// إعادة فحص الاتصال
  Future<void> _retryConnection() async {
    setState(() {
      _isChecking = true;
      _connectionStatus = 'جاري إعادة فحص الاتصال...';
    });

    await _checkInitialConnection();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1E88E5),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF1E88E5),
                      Color(0xFF1565C0),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // أيقونة الاتصال
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _isConnected
                              ? Icons.wifi
                              : _isChecking
                                  ? Icons.wifi_find
                                  : Icons.wifi_off,
                          size: 60,
                          color: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 40),
                      
                      // عنوان التطبيق
                      const Text(
                        'تطبيق التوصيل',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // حالة الاتصال
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 40),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            // مؤشر التحميل أو أيقونة الحالة
                            if (_isChecking)
                              const SizedBox(
                                width: 30,
                                height: 30,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  strokeWidth: 3,
                                ),
                              )
                            else
                              Icon(
                                _isConnected ? Icons.check_circle : Icons.error,
                                size: 30,
                                color: _isConnected ? Colors.green : Colors.red,
                              ),
                            
                            const SizedBox(height: 15),
                            
                            // نص حالة الاتصال
                            Text(
                              _connectionStatus,
                              style: const TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            // رسالة إضافية إذا لم يكن متصل
                            if (!_isConnected && !_isChecking) ...[
                              const SizedBox(height: 10),
                              const Text(
                                'يرجى التأكد من اتصالك بالإنترنت\nوالمحاولة مرة أخرى',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white70,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 40),
                      
                      // زر إعادة المحاولة (يظهر فقط عند عدم الاتصال)
                      if (!_isConnected && !_isChecking)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 60),
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _retryConnection,
                            icon: const Icon(Icons.refresh, color: Color(0xFF1E88E5)),
                            label: const Text(
                              'إعادة المحاولة',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF1E88E5),
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              elevation: 5,
                            ),
                          ),
                        ),
                      
                      // رسالة الانتقال التلقائي (تظهر عند الاتصال)
                      if (_isConnected && !_isChecking)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 40),
                          child: const Text(
                            'سيتم الانتقال تلقائياً...',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      
                      const SizedBox(height: 60),
                      
                      // معلومات إضافية
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 40),
                        child: const Text(
                          'يتطلب هذا التطبيق اتصال بالإنترنت\nللحصول على أحدث البيانات',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white60,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
