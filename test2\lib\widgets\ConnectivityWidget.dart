import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import '../services/ConnectivityService.dart';

/// Widget لعرض حالة الاتصال بالإنترنت
class ConnectivityWidget extends StatefulWidget {
  final Widget child;
  final bool showBanner;
  final Duration bannerDuration;
  final Color connectedColor;
  final Color disconnectedColor;

  const ConnectivityWidget({
    Key? key,
    required this.child,
    this.showBanner = true,
    this.bannerDuration = const Duration(seconds: 3),
    this.connectedColor = Colors.green,
    this.disconnectedColor = Colors.red,
  }) : super(key: key);

  @override
  State<ConnectivityWidget> createState() => _ConnectivityWidgetState();
}

class _ConnectivityWidgetState extends State<ConnectivityWidget>
    with TickerProviderStateMixin {
  final ConnectivityService _connectivityService = ConnectivityService();
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  
  bool _isConnected = true;
  bool _showBanner = false;
  Timer? _bannerTimer;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeConnectivity();
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  /// تهيئة مراقبة الاتصال
  void _initializeConnectivity() {
    _isConnected = _connectivityService.isConnected;
    
    // إضافة مستمع لتغييرات الاتصال
    _connectivityService.addListener(_onConnectivityChanged);
  }

  /// معالجة تغيير حالة الاتصال
  void _onConnectivityChanged(bool isConnected) {
    if (mounted && _isConnected != isConnected) {
      setState(() {
        _isConnected = isConnected;
      });

      if (widget.showBanner) {
        _showConnectivityBanner();
      }
    }
  }

  /// عرض شريط حالة الاتصال
  void _showConnectivityBanner() {
    setState(() {
      _showBanner = true;
    });

    _animationController.forward();

    // إخفاء الشريط بعد المدة المحددة
    _bannerTimer?.cancel();
    _bannerTimer = Timer(widget.bannerDuration, () {
      if (mounted) {
        _animationController.reverse().then((_) {
          if (mounted) {
            setState(() {
              _showBanner = false;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _connectivityService.removeListener(_onConnectivityChanged);
    _animationController.dispose();
    _bannerTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // المحتوى الأساسي
        widget.child,
        
        // شريط حالة الاتصال
        if (_showBanner)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildConnectivityBanner(),
            ),
          ),
      ],
    );
  }

  /// بناء شريط حالة الاتصال
  Widget _buildConnectivityBanner() {
    final backgroundColor = _isConnected 
        ? widget.connectedColor 
        : widget.disconnectedColor;
    
    final icon = _isConnected 
        ? Icons.wifi 
        : Icons.wifi_off;
    
    final message = _isConnected 
        ? _connectivityService.getConnectionStatusMessage()
        : 'غير متصل بالإنترنت';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget مبسط لعرض أيقونة حالة الاتصال
class ConnectivityIndicator extends StatefulWidget {
  final double size;
  final Color? connectedColor;
  final Color? disconnectedColor;
  final bool showText;

  const ConnectivityIndicator({
    Key? key,
    this.size = 24,
    this.connectedColor,
    this.disconnectedColor,
    this.showText = false,
  }) : super(key: key);

  @override
  State<ConnectivityIndicator> createState() => _ConnectivityIndicatorState();
}

class _ConnectivityIndicatorState extends State<ConnectivityIndicator> {
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();
    _isConnected = _connectivityService.isConnected;
    _connectivityService.addListener(_onConnectivityChanged);
  }

  void _onConnectivityChanged(bool isConnected) {
    if (mounted) {
      setState(() {
        _isConnected = isConnected;
      });
    }
  }

  @override
  void dispose() {
    _connectivityService.removeListener(_onConnectivityChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = _isConnected
        ? (widget.connectedColor ?? Colors.green)
        : (widget.disconnectedColor ?? Colors.red);

    final icon = _isConnected ? Icons.wifi : Icons.wifi_off;

    if (widget.showText) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: widget.size,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            _connectivityService.getConnectionIcon(),
            style: TextStyle(
              fontSize: widget.size * 0.6,
              color: color,
            ),
          ),
        ],
      );
    }

    return Icon(
      icon,
      size: widget.size,
      color: color,
    );
  }
}

/// Widget للتحقق من الاتصال قبل تنفيذ عملية
class ConnectivityChecker extends StatelessWidget {
  final Widget child;
  final VoidCallback? onNoConnection;
  final String? noConnectionMessage;

  const ConnectivityChecker({
    Key? key,
    required this.child,
    this.onNoConnection,
    this.noConnectionMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ConnectivityResult>(
      stream: Connectivity().onConnectivityChanged,
      builder: (context, snapshot) {
        final isConnected = snapshot.hasData &&
            snapshot.data! != ConnectivityResult.none;

        if (!isConnected) {
          return _buildNoConnectionWidget(context);
        }

        return child;
      },
    );
  }

  Widget _buildNoConnectionWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.wifi_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            noConnectionMessage ?? 'لا يوجد اتصال بالإنترنت',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onNoConnection ?? () {
              // إعادة تحميل الصفحة أو إظهار رسالة
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('يرجى التحقق من اتصالك بالإنترنت'),
                ),
              );
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
