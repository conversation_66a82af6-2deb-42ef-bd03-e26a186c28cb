import 'package:geolocator/geolocator.dart';

/// نموذج بيانات تتبع الطلب
class OrderTrackingData {
  final String orderId;
  final Position userPosition;
  final Position driverPosition;
  final double distance; // بالمتر
  final int estimatedTime; // بالثواني
  final String status;
  final DateTime lastUpdate;

  OrderTrackingData({
    required this.orderId,
    required this.userPosition,
    required this.driverPosition,
    required this.distance,
    required this.estimatedTime,
    required this.status,
    required this.lastUpdate,
  });

  /// تحويل المسافة إلى نص قابل للقراءة
  String get formattedDistance {
    if (distance < 1000) {
      return '${distance.round()} متر';
    } else {
      return '${(distance / 1000).toStringAsFixed(1)} كم';
    }
  }

  /// تحويل الوقت المتوقع إلى نص قابل للقراءة
  String get formattedEstimatedTime {
    if (estimatedTime < 60) {
      return '$estimatedTime ثانية';
    } else if (estimatedTime < 3600) {
      int minutes = (estimatedTime / 60).round();
      return '$minutes دقيقة';
    } else {
      int hours = (estimatedTime / 3600).floor();
      int minutes = ((estimatedTime % 3600) / 60).round();
      return '$hours ساعة و $minutes دقيقة';
    }
  }

  /// نسخ البيانات مع تحديث بعض القيم
  OrderTrackingData copyWith({
    String? orderId,
    Position? userPosition,
    Position? driverPosition,
    double? distance,
    int? estimatedTime,
    String? status,
    DateTime? lastUpdate,
  }) {
    return OrderTrackingData(
      orderId: orderId ?? this.orderId,
      userPosition: userPosition ?? this.userPosition,
      driverPosition: driverPosition ?? this.driverPosition,
      distance: distance ?? this.distance,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      status: status ?? this.status,
      lastUpdate: lastUpdate ?? this.lastUpdate,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'orderId': orderId,
      'userPosition': {
        'latitude': userPosition.latitude,
        'longitude': userPosition.longitude,
        'timestamp': userPosition.timestamp.millisecondsSinceEpoch,
      },
      'driverPosition': {
        'latitude': driverPosition.latitude,
        'longitude': driverPosition.longitude,
        'timestamp': driverPosition.timestamp.millisecondsSinceEpoch,
      },
      'distance': distance,
      'estimatedTime': estimatedTime,
      'status': status,
      'lastUpdate': lastUpdate.millisecondsSinceEpoch,
    };
  }

  /// إنشاء من Map
  factory OrderTrackingData.fromMap(Map<String, dynamic> map) {
    return OrderTrackingData(
      orderId: map['orderId'] ?? '',
      userPosition: Position(
        latitude: map['userPosition']['latitude'] ?? 0.0,
        longitude: map['userPosition']['longitude'] ?? 0.0,
        timestamp: DateTime.fromMillisecondsSinceEpoch(map['userPosition']['timestamp'] ?? 0),
        accuracy: 10.0,
        altitude: 0.0,
        heading: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      ),
      driverPosition: Position(
        latitude: map['driverPosition']['latitude'] ?? 0.0,
        longitude: map['driverPosition']['longitude'] ?? 0.0,
        timestamp: DateTime.fromMillisecondsSinceEpoch(map['driverPosition']['timestamp'] ?? 0),
        accuracy: 10.0,
        altitude: 0.0,
        heading: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      ),
      distance: map['distance']?.toDouble() ?? 0.0,
      estimatedTime: _safeToInt(map['estimatedTime']),
      status: map['status'] ?? '',
      lastUpdate: DateTime.fromMillisecondsSinceEpoch(map['lastUpdate'] ?? 0),
    );
  }

  /// تحويل آمن إلى int
  static int _safeToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) {
      if (value.isNaN || value.isInfinite) return 0;
      return value.toInt();
    }
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  @override
  String toString() {
    return 'OrderTrackingData(orderId: $orderId, distance: ${formattedDistance}, estimatedTime: ${formattedEstimatedTime}, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderTrackingData &&
        other.orderId == orderId &&
        other.userPosition.latitude == userPosition.latitude &&
        other.userPosition.longitude == userPosition.longitude &&
        other.driverPosition.latitude == driverPosition.latitude &&
        other.driverPosition.longitude == driverPosition.longitude &&
        other.distance == distance &&
        other.estimatedTime == estimatedTime &&
        other.status == status;
  }

  @override
  int get hashCode {
    return orderId.hashCode ^
        userPosition.latitude.hashCode ^
        userPosition.longitude.hashCode ^
        driverPosition.latitude.hashCode ^
        driverPosition.longitude.hashCode ^
        distance.hashCode ^
        estimatedTime.hashCode ^
        status.hashCode;
  }
}
