import 'package:firebase_database/firebase_database.dart';
import '../models/Order.dart';
import '../models/CartItem.dart';

/// خدمة إرسال الطلبات إلى Firebase Realtime Database
class RealtimeOrderService {
  static final RealtimeOrderService _instance = RealtimeOrderService._internal();
  factory RealtimeOrderService() => _instance;
  RealtimeOrderService._internal();

  final DatabaseReference _database = FirebaseDatabase.instance.ref();

  /// التحقق من الاتصال بـ Firebase Realtime Database
  Future<bool> _testConnection() async {
    try {
      print('🔍 اختبار الاتصال بـ Firebase Realtime Database...');

      // محاولة كتابة بيانات اختبار
      final testRef = _database.child('connection_test');
      await testRef.set({
        'timestamp': DateTime.now().toIso8601String(),
        'test': true,
      });

      print('✅ تم الاتصال بـ Firebase Realtime Database بنجاح');

      // حذف بيانات الاختبار
      await testRef.remove();

      return true;
    } catch (e) {
      print('❌ فشل في الاتصال بـ Firebase Realtime Database: $e');
      return false;
    }
  }

  /// إرسال الطلب إلى المحلات المختلفة في Realtime Database
  Future<bool> sendOrderToStores(Order order) async {
    try {
      print('🚀 RealtimeOrderService: بدء إرسال الطلب ${order.id} إلى المحلات في Realtime Database...');
      print('📦 عدد المنتجات في الطلب: ${order.items.length}');

      // اختبار الاتصال أولاً
      final connectionOk = await _testConnection();
      if (!connectionOk) {
        print('❌ فشل في الاتصال بـ Firebase Realtime Database');
        return false;
      }

      // تجميع المنتجات حسب المحل
      Map<String, List<CartItem>> storeOrders = _groupItemsByStore(order.items);
      print('🏪 عدد المحلات المختلفة: ${storeOrders.length}');

      for (String storeId in storeOrders.keys) {
        print('🏪 محل: $storeId - عدد المنتجات: ${storeOrders[storeId]!.length}');
      }

      // إرسال كل مجموعة منتجات للمحل المناسب
      List<Future<void>> sendTasks = [];

      for (String storeId in storeOrders.keys) {
        final storeItems = storeOrders[storeId]!;
        print('📝 إنشاء بيانات الطلب للمحل: $storeId');
        final storeOrderData = _createStoreOrderData(order, storeItems, storeId);

        print('📤 إضافة مهمة إرسال للمحل: $storeId');
        // إرسال للمحل المحدد
        sendTasks.add(_sendToStore(storeId, order.id, storeOrderData));
      }

      print('⏳ انتظار إرسال جميع الطلبات...');
      // انتظار إرسال جميع الطلبات
      await Future.wait(sendTasks);

      print('✅ تم إرسال الطلب ${order.id} بنجاح إلى ${storeOrders.length} محل في Realtime Database');
      return true;

    } catch (e) {
      print('💥 خطأ في إرسال الطلب إلى Realtime Database: $e');
      print('📊 تفاصيل الخطأ: ${e.toString()}');
      return false;
    }
  }

  /// تجميع المنتجات حسب المحل
  Map<String, List<CartItem>> _groupItemsByStore(List<CartItem> items) {
    Map<String, List<CartItem>> storeGroups = {};

    for (CartItem item in items) {
      String storeId = (item.storeId != null && item.storeId!.isNotEmpty)
          ? item.storeId!
          : 'default_store';

      if (!storeGroups.containsKey(storeId)) {
        storeGroups[storeId] = [];
      }
      storeGroups[storeId]!.add(item);
    }

    return storeGroups;
  }

  /// إنشاء بيانات الطلب للمحل المحدد
  Map<String, dynamic> _createStoreOrderData(Order order, List<CartItem> storeItems, String storeId) {
    // حساب المجموع الفرعي للمحل
    double storeSubtotal = storeItems.fold(0.0, (sum, item) => sum + item.totalPrice);

    return {
      'orderId': order.id,
      'customerId': order.userId,
      'customerName': order.userName,
      'customerPhone': order.userPhone,
      'storeId': storeId,
      'status': 'pending', // حالة الطلب: pending, confirmed, preparing, ready, delivered, cancelled
      'orderDate': order.createdAt.toIso8601String(),
      'deliveryAddress': {
        'id': order.deliveryAddress.id,
        'title': order.deliveryAddress.title,
        'street': order.deliveryAddress.street,
        'district': order.deliveryAddress.district,
        'city': order.deliveryAddress.city,
        'buildingNumber': order.deliveryAddress.buildingNumber,
        'fullAddress': order.deliveryAddress.fullAddress,
      },
      'items': storeItems.map((item) => {
        'productId': item.productId,
        'productName': item.name,
        'productDescription': item.description,
        'productImage': item.imageUrl,
        'quantity': item.quantity,
        'unitPrice': item.price,
        'totalPrice': item.totalPrice,
        'notes': '', // CartItem لا يحتوي على notes
      }).toList(),
      'storeSubtotal': storeSubtotal,
      'paymentMethod': {
        'id': order.paymentMethod.id,
        'name': order.paymentMethod.name,
        'type': order.paymentMethod.type,
        'isEnabled': order.paymentMethod.isEnabled,
        'description': order.paymentMethod.description,
        'icon': order.paymentMethod.icon,
      },
      'deliveryTime': order.deliveryTime,
      'scheduledTime': order.scheduledTime?.toIso8601String(),
      'notes': order.notes,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// إرسال الطلب لمحل محدد
  Future<void> _sendToStore(String storeId, String orderId, Map<String, dynamic> orderData) async {
    try {
      print('📤 بدء إرسال الطلب $orderId للمحل $storeId...');

      // مسار المحل في Realtime Database
      final storeOrdersRef = _database.child('store_orders').child(storeId).child(orderId);
      print('📍 مسار Realtime Database: store_orders/$storeId/$orderId');

      print('📊 بيانات الطلب المرسلة:');
      print('   - اسم العميل: ${orderData['customerName']}');
      print('   - عدد المنتجات: ${(orderData['items'] as List).length}');
      print('   - المجموع الفرعي: ${orderData['storeSubtotal']}');

      // إرسال البيانات
      print('🔄 إرسال البيانات إلى Realtime Database...');
      await storeOrdersRef.set(orderData);
      print('✅ تم إرسال البيانات بنجاح');

      // إضافة إشعار للمحل
      print('🔔 إرسال إشعار للمحل...');
      await _sendStoreNotification(storeId, orderId, orderData);

      print('✅ تم إرسال الطلب $orderId للمحل $storeId بنجاح في Realtime Database');

    } catch (e) {
      print('💥 خطأ في إرسال الطلب $orderId للمحل $storeId: $e');
      print('📊 تفاصيل الخطأ: ${e.toString()}');
      rethrow;
    }
  }

  /// إرسال إشعار للمحل بالطلب الجديد
  Future<void> _sendStoreNotification(String storeId, String orderId, Map<String, dynamic> orderData) async {
    try {
      final notificationRef = _database.child('store_notifications').child(storeId).push();
      
      await notificationRef.set({
        'type': 'new_order',
        'orderId': orderId,
        'customerName': orderData['customerName'],
        'itemsCount': (orderData['items'] as List).length,
        'totalAmount': orderData['storeSubtotal'],
        'message': 'طلب جديد من ${orderData['customerName']}',
        'timestamp': DateTime.now().toIso8601String(),
        'isRead': false,
      });

      print('تم إرسال إشعار للمحل $storeId للطلب $orderId');

    } catch (e) {
      print('خطأ في إرسال إشعار للمحل $storeId: $e');
    }
  }

  /// تحديث حالة الطلب في محل محدد
  Future<bool> updateOrderStatus(String storeId, String orderId, String newStatus) async {
    try {
      final orderRef = _database.child('store_orders').child(storeId).child(orderId);
      
      await orderRef.update({
        'status': newStatus,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      print('تم تحديث حالة الطلب $orderId في المحل $storeId إلى $newStatus');
      return true;

    } catch (e) {
      print('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// الحصول على طلبات محل محدد
  Stream<Map<String, dynamic>> getStoreOrders(String storeId) {
    return _database
        .child('store_orders')
        .child(storeId)
        .onValue
        .map((event) {
      if (event.snapshot.value != null) {
        return Map<String, dynamic>.from(event.snapshot.value as Map);
      }
      return <String, dynamic>{};
    });
  }

  /// الحصول على إشعارات محل محدد
  Stream<List<Map<String, dynamic>>> getStoreNotifications(String storeId) {
    return _database
        .child('store_notifications')
        .child(storeId)
        .orderByChild('timestamp')
        .onValue
        .map((event) {
      List<Map<String, dynamic>> notifications = [];
      
      if (event.snapshot.value != null) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        
        data.forEach((key, value) {
          final notification = Map<String, dynamic>.from(value);
          notification['id'] = key;
          notifications.add(notification);
        });
        
        // ترتيب حسب الوقت (الأحدث أولاً)
        notifications.sort((a, b) => 
            DateTime.parse(b['timestamp']).compareTo(DateTime.parse(a['timestamp'])));
      }
      
      return notifications;
    });
  }

  /// وضع علامة قراءة على إشعار
  Future<void> markNotificationAsRead(String storeId, String notificationId) async {
    try {
      await _database
          .child('store_notifications')
          .child(storeId)
          .child(notificationId)
          .update({'isRead': true});
    } catch (e) {
      print('خطأ في وضع علامة قراءة على الإشعار: $e');
    }
  }

  /// حذف طلب من محل محدد
  Future<bool> deleteStoreOrder(String storeId, String orderId) async {
    try {
      await _database.child('store_orders').child(storeId).child(orderId).remove();
      print('تم حذف الطلب $orderId من المحل $storeId');
      return true;
    } catch (e) {
      print('خطأ في حذف الطلب: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المحل
  Future<Map<String, dynamic>> getStoreStatistics(String storeId) async {
    try {
      final snapshot = await _database.child('store_orders').child(storeId).get();
      
      if (!snapshot.exists) {
        return {
          'totalOrders': 0,
          'pendingOrders': 0,
          'completedOrders': 0,
          'totalRevenue': 0.0,
        };
      }

      final orders = Map<String, dynamic>.from(snapshot.value as Map);
      int totalOrders = orders.length;
      int pendingOrders = 0;
      int completedOrders = 0;
      double totalRevenue = 0.0;

      orders.forEach((orderId, orderData) {
        final order = Map<String, dynamic>.from(orderData);
        final status = order['status'] as String;
        
        if (status == 'pending' || status == 'confirmed' || status == 'preparing') {
          pendingOrders++;
        } else if (status == 'delivered') {
          completedOrders++;
          totalRevenue += (order['storeSubtotal'] as num).toDouble();
        }
      });

      return {
        'totalOrders': totalOrders,
        'pendingOrders': pendingOrders,
        'completedOrders': completedOrders,
        'totalRevenue': totalRevenue,
      };

    } catch (e) {
      print('خطأ في الحصول على إحصائيات المحل: $e');
      return {
        'totalOrders': 0,
        'pendingOrders': 0,
        'completedOrders': 0,
        'totalRevenue': 0.0,
      };
    }
  }
}
