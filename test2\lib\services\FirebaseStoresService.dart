import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;
import '../models/Store.dart';

/// خدمة Firebase لإدارة المحلات
class FirebaseStoresService {
  static final FirebaseStoresService _instance = FirebaseStoresService._internal();
  factory FirebaseStoresService() => _instance;
  FirebaseStoresService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// مجموعة المحلات في Firestore
  static const String storesCollection = 'all_stores';

  /// جلب جميع المحلات من Firebase
  Future<List<Store>> getAllStores() async {
    try {
      final querySnapshot = await _firestore
          .collection(storesCollection)
          .orderBy('createdAt', descending: false)
          .get();

      List<Store> stores = [];
      for (var doc in querySnapshot.docs) {
        try {
          final store = Store.fromFirestore(doc);
          stores.add(store);
        } catch (e) {
          print('خطأ في تحويل المحل ${doc.id}: $e');
          // تجاهل المحل المعطوب ومتابعة باقي المحلات
        }
      }

      print('تم تحميل ${stores.length} محل من Firebase');
      return stores;
    } catch (e) {
      print('خطأ في جلب المحلات: $e');
      return [];
    }
  }

  /// جلب المحلات حسب الفئة
  Future<List<Store>> getStoresByCategory(String category) async {
    try {
      Query query = _firestore.collection(storesCollection);
      
      if (category != "الكل") {
        query = query.where('store_categ', isEqualTo: category);
      }
      
      final querySnapshot = await query
          .orderBy('createdAt', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Store.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('خطأ في جلب المحلات حسب الفئة: $e');
      return [];
    }
  }

  /// مراقبة التغييرات في المحلات (Real-time)
  Stream<List<Store>> watchStores() {
    return _firestore
        .collection(storesCollection)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) {
      List<Store> stores = [];

      if (snapshot.docs.isEmpty) {
        print('لا توجد محلات في Firebase');
        return stores;
      }

      for (var doc in snapshot.docs) {
        try {
          // التأكد من وجود البيانات في المستند
          if (doc.exists && doc.data() != null) {
            final store = Store.fromFirestore(doc);
            stores.add(store);
          } else {
            print('مستند فارغ أو غير موجود: ${doc.id}');
          }
        } catch (e) {
          print('خطأ في تحويل المحل ${doc.id}: $e');
          // تجاهل المحل المعطوب ومتابعة باقي المحلات
        }
      }

      print('تم تحميل ${stores.length} محل من Firebase Stream');
      return stores;
    }).handleError((error) {
      print('خطأ في Firebase Stream: $error');
      return <Store>[]; // إرجاع قائمة فارغة في حالة الخطأ
    });
  }

  /// مراقبة التغييرات في المحلات حسب الفئة
  Stream<List<Store>> watchStoresByCategory(String category) {
    Query query = _firestore.collection(storesCollection);
    
    if (category != "الكل") {
      query = query.where('store_categ', isEqualTo: category);
    }
    
    return query
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Store.fromFirestore(doc))
          .toList();
    });
  }

  /// مراقبة المحلات الجديدة فقط (للإشعارات)
  Stream<List<Store>> watchNewStores() {
    // نراقب المحلات التي تم إنشاؤها في آخر دقيقة
    final oneMinuteAgo = DateTime.now().subtract(Duration(minutes: 1));
    
    return _firestore
        .collection(storesCollection)
        .where('createdAt', isGreaterThan: Timestamp.fromDate(oneMinuteAgo))
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Store.fromFirestore(doc))
          .toList();
    });
  }

  /// إضافة محل جديد (للاختبار)
  Future<bool> addStore(Store store) async {
    try {
      await _firestore
          .collection(storesCollection)
          .doc(store.id)
          .set(store.toFirestore());
      
      print('تم إضافة المحل بنجاح: ${store.name}');
      return true;
    } catch (e) {
      print('خطأ في إضافة المحل: $e');
      return false;
    }
  }

  /// تحديث محل موجود
  Future<bool> updateStore(Store store) async {
    try {
      await _firestore
          .collection(storesCollection)
          .doc(store.id)
          .update(store.toFirestore());
      
      print('تم تحديث المحل بنجاح: ${store.name}');
      return true;
    } catch (e) {
      print('خطأ في تحديث المحل: $e');
      return false;
    }
  }

  /// حذف محل
  Future<bool> deleteStore(String storeId) async {
    try {
      await _firestore
          .collection(storesCollection)
          .doc(storeId)
          .delete();
      
      print('تم حذف المحل بنجاح: $storeId');
      return true;
    } catch (e) {
      print('خطأ في حذف المحل: $e');
      return false;
    }
  }

  /// البحث في المحلات
  Future<List<Store>> searchStores(String searchTerm) async {
    try {
      // البحث بالاسم
      final nameQuery = await _firestore
          .collection(storesCollection)
          .where('store_name', isGreaterThanOrEqualTo: searchTerm)
          .where('store_name', isLessThanOrEqualTo: searchTerm + '\uf8ff')
          .get();

      // البحث بالفئة
      final categoryQuery = await _firestore
          .collection(storesCollection)
          .where('category', isEqualTo: searchTerm)
          .get();

      // دمج النتائج وإزالة التكرار
      Set<String> seenIds = {};
      List<Store> results = [];

      for (var doc in [...nameQuery.docs, ...categoryQuery.docs]) {
        if (!seenIds.contains(doc.id)) {
          seenIds.add(doc.id);
          results.add(Store.fromFirestore(doc));
        }
      }

      return results;
    } catch (e) {
      print('خطأ في البحث: $e');
      return [];
    }
  }

  /// جلب محل واحد بالمعرف
  Future<Store?> getStoreById(String storeId) async {
    try {
      final doc = await _firestore
          .collection(storesCollection)
          .doc(storeId)
          .get();

      if (doc.exists) {
        return Store.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('خطأ في جلب المحل: $e');
      return null;
    }
  }

  /// تحديث حالة المحل (مفتوح/مغلق)
  Future<bool> updateStoreStatus(String storeId, bool isOpen) async {
    try {
      await _firestore
          .collection(storesCollection)
          .doc(storeId)
          .update({
        'isOpen': isOpen,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      return true;
    } catch (e) {
      print('خطأ في تحديث حالة المحل: $e');
      return false;
    }
  }

  /// إضافة بيانات تجريبية (للاختبار)
  Future<void> addSampleStores() async {
    final sampleStores = [
      Store(
        id: 'store_1',
        name: 'مطعم الشرق',
        category: 'مطاعم',
        rating: 4.8,
        deliveryTime: 35,
        image: 'images/1.png',
        isOpen: true,
        address: 'شارع الزبيري، صنعاء',
        phone: '+967771234567',
        description: 'مطعم متخصص في الأكلات الشرقية والعربية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Store(
        id: 'store_2',
        name: 'سوبرماركت الأمانة',
        category: 'سوبر ماركت',
        rating: 4.5,
        deliveryTime: 25,
        image: 'images/2.png',
        isOpen: true,
        address: 'شارع الستين، صنعاء',
        phone: '+967771234568',
        description: 'سوبرماركت شامل لجميع احتياجاتك اليومية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Store(
        id: 'store_3',
        name: 'صيدلية الصحة',
        category: 'صيدليات',
        rating: 4.7,
        deliveryTime: 20,
        image: 'images/3.png',
        isOpen: true,
        address: 'شارع الحصبة، صنعاء',
        phone: '+967771234569',
        description: 'صيدلية متكاملة مع جميع الأدوية والمستلزمات الطبية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (var store in sampleStores) {
      await addStore(store);
    }
  }
}
