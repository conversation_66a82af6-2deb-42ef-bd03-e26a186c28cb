import 'package:flutter/material.dart';
import 'auth.dart';

class PhoneAuthScreen extends StatefulWidget {
  const PhoneAuthScreen({super.key});

  @override
  State<PhoneAuthScreen> createState() => _PhoneAuthScreenState();
}

class _PhoneAuthScreenState extends State<PhoneAuthScreen> {
  final _authService = AuthService();
  final _phoneController = TextEditingController();
  final _otpController = TextEditingController();

  bool _codeSent = false;
  bool _isLoading = false;

  void _sendCode() async {
    setState(() => _isLoading = true);

    await _authService.sendOtp(
      phoneNumber: _phoneController.text.trim(),
      onCodeSent: (verificationId) {
        setState(() {
          _codeSent = true;
          _isLoading = false;
        });
        _showMessage("تم إرسال كود التحقق");
      },
      onAutoVerified: (credential) async {
        await _authService.verifyOtp(smsCode: credential.smsCode!);
        _showMessage("تم التحقق تلقائيًا");
      },
      onFailed: (error) {
        setState(() => _isLoading = false);
        _showMessage("فشل: ${error.message}");
      },
    );
  }

  void _verifyCode() async {
    setState(() => _isLoading = true);
    try {
      await _authService.verifyOtp(smsCode: _otpController.text.trim());
      _showMessage("تم التحقق بنجاح 🎉");
    } catch (e) {
      _showMessage("الرمز غير صحيح");
    }
    setState(() => _isLoading = false);
  }

  void _showMessage(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(msg), backgroundColor: Colors.black),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('تسجيل الدخول برقم الهاتف')),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  if (!_codeSent)
                    TextField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        labelText: 'رقم الهاتف',
                        hintText: '+9677XXXXXXXX',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  if (_codeSent)
                    TextField(
                      controller: _otpController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'رمز التحقق (OTP)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _codeSent ? _verifyCode : _sendCode,
                    child: Text(_codeSent ? 'تحقق من الرمز' : 'إرسال الرمز'),
                  ),
                ],
              ),
            ),
    );
  }
}
