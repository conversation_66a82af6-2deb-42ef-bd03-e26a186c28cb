{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-39:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\403f29e94df891a2dd1d4561277e8087\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "122,142", "startColumns": "4,4", "startOffsets": "7461,9399", "endColumns": "67,166", "endOffsets": "7524,9561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\26e1d6f843b8bd9c55fbe9cde85475ac\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "17,18,19,20,36,37,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,979,1045,1108,2337,2408,10692,10760,10827,10906", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "974,1040,1103,1165,2403,2475,10755,10822,10901,10970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed293fa1fecc38363e0267349aa1d19f\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "23,24,25,26,27,28,29,30,134,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,151,332,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1301,1391,1471,1561,1651,1731,1812,1892,8359,8464,8645,8770,8877,9057,9180,9296,9566,9754,9859,10040,10165,10340,10488,10551,10613,18683,20505", "endLines": "23,24,25,26,27,28,29,30,134,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,151,344,426", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1386,1466,1556,1646,1726,1807,1887,1967,8459,8640,8765,8872,9052,9175,9291,9394,9749,9854,10035,10160,10335,10483,10546,10608,10687,18993,20917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1bc5e3771ddad17cb7465c50c7aa900d\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "33,34,35,60,61,62,63,121,176,178,179,184,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2104,2193,2264,3946,3999,4052,4105,7401,12378,12554,12676,12938,13133", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "2188,2259,2332,3994,4047,4100,4153,7456,12439,12671,12732,12999,13195"}}, {"source": "D:\\augments_projects\\appss\\test2\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,569", "endColumns": "81,103,108,119,98,67", "endOffsets": "132,236,345,465,564,632"}, "to": {"startLines": "157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4", "startOffsets": "11057,11139,11243,11352,11472,11571", "endColumns": "81,103,108,119,98,67", "endOffsets": "11134,11238,11347,11467,11566,11634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\467f116ae41acb0fa99753049275ea75\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "7599,7681", "endColumns": "81,83", "endOffsets": "7676,7760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\439e89e84779b59d76ae02cb100103cf\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "191", "startColumns": "4", "startOffsets": "13423", "endLines": "198", "endColumns": "8", "endOffsets": "13828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af735b56e9cba6b1942a99866ff67516\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "11,12,13,14", "startColumns": "4,4,4,4", "startOffsets": "501,566,636,700", "endColumns": "64,69,63,60", "endOffsets": "561,631,695,756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\745573af90b7d28a08508c3615472ffb\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "7351", "endColumns": "49", "endOffsets": "7396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6de874d0ddfc66f7664f75b89ce9d9df\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "7308", "endColumns": "42", "endOffsets": "7346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10eaf2ffd214251ac1191e292ccbd398\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "10975", "endColumns": "81", "endOffsets": "11052"}}, {"source": "D:\\augments_projects\\appss\\test2\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "164,168", "startColumns": "4,4", "startOffsets": "11710,11891", "endLines": "167,170", "endColumns": "12,12", "endOffsets": "11886,12055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4331d38e1b5b1898f63e3abba3c0c6d2\\transformed\\jetified-window-1.0.0-beta04\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "3,8,9,10,100,213,219,427,435,447", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "165,344,404,456,6319,14592,14787,20922,21204,21644", "endLines": "7,8,9,10,100,218,222,434,446,454", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "339,399,451,496,6374,14782,14913,21199,21639,21948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4aca9238de64e93789858c4e0fabfeba\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "187", "startColumns": "4", "startOffsets": "13200", "endLines": "190", "endColumns": "12", "endOffsets": "13418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\50b502852e61bf607df9cdf3b57dfad2\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "126", "startColumns": "4", "startOffsets": "7765", "endColumns": "82", "endOffsets": "7843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3927d342be7108a61372589dd032352d\\transformed\\core-1.13.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,15,16,21,22,31,32,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,123,127,128,129,130,131,132,133,163,171,172,177,180,185,199,200,223,229,239,272,293,326", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,761,833,1170,1235,1972,2041,2480,2550,2618,2690,2760,2821,2895,2968,3029,3090,3152,3216,3278,3339,3407,3507,3567,3633,3706,3775,3832,3884,4158,4230,4306,4371,4430,4489,4549,4609,4669,4729,4789,4849,4909,4969,5029,5089,5148,5208,5268,5328,5388,5448,5508,5568,5628,5688,5748,5807,5867,5927,5986,6045,6104,6163,6222,6379,6414,6449,6504,6567,6622,6680,6738,6799,6862,6919,6970,7020,7081,7138,7204,7238,7273,7529,7848,7915,7987,8056,8125,8199,8271,11639,12060,12177,12444,12737,13004,13833,13905,14918,15121,15422,17153,17834,18516", "endLines": "2,15,16,21,22,31,32,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,123,127,128,129,130,131,132,133,163,171,175,177,183,185,199,200,228,238,271,292,325,331", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "160,828,916,1230,1296,2036,2099,2545,2613,2685,2755,2816,2890,2963,3024,3085,3147,3211,3273,3334,3402,3502,3562,3628,3701,3770,3827,3879,3941,4225,4301,4366,4425,4484,4544,4604,4664,4724,4784,4844,4904,4964,5024,5084,5143,5203,5263,5323,5383,5443,5503,5563,5623,5683,5743,5802,5862,5922,5981,6040,6099,6158,6217,6276,6409,6444,6499,6562,6617,6675,6733,6794,6857,6914,6965,7015,7076,7133,7199,7233,7268,7303,7594,7910,7982,8051,8120,8194,8266,8354,11705,12172,12373,12549,12933,13128,13900,13967,15116,15417,17148,17829,18511,18678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db36947d2d0e3bbea45e70638ee3951f\\transformed\\jetified-android-maps-utils-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "99,201,205,208", "startColumns": "4,4,4,4", "startOffsets": "6281,13972,14180,14340", "endLines": "99,204,207,212", "endColumns": "37,12,12,12", "endOffsets": "6314,14175,14335,14587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0e801fb926a3d574c06ff316d2665fce\\transformed\\jetified-play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "345", "startColumns": "4", "startOffsets": "18998", "endLines": "407", "endColumns": "20", "endOffsets": "20500"}}]}]}