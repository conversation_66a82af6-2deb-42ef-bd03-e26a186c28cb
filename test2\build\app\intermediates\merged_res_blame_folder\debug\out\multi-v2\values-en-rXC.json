{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-39:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3927d342be7108a61372589dd032352d\\transformed\\core-1.13.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "4,5,6,7,8,9,10,15", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "486,682,887,1088,1289,1496,1701,2725", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "677,882,1083,1284,1491,1696,1908,2924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\26e1d6f843b8bd9c55fbe9cde85475ac\\transformed\\browser-1.4.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "11,12,13,14", "startColumns": "4,4,4,4", "startOffsets": "1913,2113,2312,2523", "endColumns": "199,198,210,201", "endOffsets": "2108,2307,2518,2720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\467f116ae41acb0fa99753049275ea75\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,270", "endColumns": "214,215", "endOffsets": "265,481"}}]}]}