import 'package:flutter/material.dart';
import 'package:test2/pages/RegistrationPage.dart';
import 'package:test2/pages/HomePages.dart';
import 'package:test2/services/UserService.dart';
import 'package:test2/utils/ResponsiveHelper.dart';
import 'package:test2/widgets/ResponsiveText.dart';
import 'package:test2/widgets/ResponsiveButton.dart';
import 'package:test2/widgets/ResponsiveContainer.dart';

class WelcomePage extends StatefulWidget {
  @override
  _WelcomePageState createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();

    // إعداد الانيميشن
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    // بدء الانيميشن
    _fadeController.forward();
    Future.delayed(Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4C53A5),
              Color(0xFF6366F1),
              Color(0xFF8B5CF6),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: Padding(
                padding: ResponsiveHelper.responsiveEdgeInsets(
                  context,
                  mobile: 16,
                  tablet: 24,
                  desktop: 32,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الجزء الأول - الشعار والعنوان
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // شعار التطبيق
                          ResponsiveContainer(
                            child: Container(
                              padding: ResponsiveHelper.responsiveEdgeInsets(
                                context,
                                mobile: 20,
                                tablet: 30,
                                desktop: 40,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(
                                  ResponsiveHelper.responsiveSize(
                                    context,
                                    mobile: 20,
                                    tablet: 25,
                                    desktop: 30,
                                  ),
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: ResponsiveHelper.responsiveSize(
                                      context,
                                      mobile: 10,
                                      tablet: 15,
                                      desktop: 20,
                                    ),
                                    offset: Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.shopping_bag,
                                size: ResponsiveHelper.responsiveSize(
                                  context,
                                  mobile: 80,
                                  tablet: 100,
                                  desktop: 120,
                                ),
                                color: Colors.white,
                              ),
                            ),
                          ),

                          SizedBox(height: ResponsiveHelper.responsiveHeight(context, percentage: 3)),

                          // عنوان التطبيق
                          ResponsiveTitle(
                            'مرحباً بك في تطبيق التوصيل',
                            color: Colors.white,
                            textAlign: TextAlign.center,
                            fontWeight: FontWeight.bold,
                          ),

                          SizedBox(height: ResponsiveHelper.responsiveHeight(context, percentage: 2)),

                          // وصف التطبيق
                          ResponsiveSubtitle(
                            'اطلب ما تشاء من المحلات المحلية\nوسيصلك في أسرع وقت',
                            color: Colors.white.withOpacity(0.9),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // مساحة فاصلة
                    SizedBox(height: ResponsiveHelper.responsiveHeight(context, percentage: 5)),

                    // الجزء الثاني - الأزرار
                    SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // رسالة ترحيبية
                          ResponsiveContainer(
                            child: ResponsiveBody(
                              'ابدأ رحلة التسوق الآن واستمتع بتجربة فريدة',
                              color: Colors.white.withOpacity(0.8),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          SizedBox(height: ResponsiveHelper.responsiveHeight(context, percentage: 4)),

                          // زر إنشاء حساب جديد
                          ResponsiveButton(
                            text: 'إنشاء حساب جديد',
                            onPressed: _goToRegistration,
                            backgroundColor: Colors.white,
                            textColor: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(
                              ResponsiveHelper.responsiveSize(
                                context,
                                mobile: 12,
                                tablet: 15,
                                desktop: 18,
                              ),
                            ),
                            padding: ResponsiveHelper.responsiveEdgeInsets(
                              context,
                              mobile: 16,
                              tablet: 20,
                              desktop: 24,
                            ),
                          ),

                          SizedBox(height: ResponsiveHelper.responsiveHeight(context, percentage: 2)),

                          // زر الدخول كضيف
                          ResponsiveButton(
                            text: 'الدخول كضيف',
                            onPressed: _goToHome,
                            backgroundColor: Colors.transparent,
                            textColor: Colors.white,
                            borderRadius: BorderRadius.circular(
                              ResponsiveHelper.responsiveSize(
                                context,
                                mobile: 12,
                                tablet: 15,
                                desktop: 18,
                              ),
                            ),
                            padding: ResponsiveHelper.responsiveEdgeInsets(
                              context,
                              mobile: 16,
                              tablet: 20,
                              desktop: 24,
                            ),
                          ),

                          SizedBox(height: ResponsiveHelper.responsiveHeight(context, percentage: 3)),

                          // ملاحظة
                          ResponsiveContainer(
                            child: ResponsiveCaption(
                              'يمكنك تسجيل حساب لاحقاً من الإعدادات',
                              color: Colors.white.withOpacity(0.7),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _goToRegistration() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => RegistrationPage()),
    );
  }

  void _goToHome() async {
    // تسجيل دخول كضيف
    await _userService.loginAsGuest();

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => HomePage()),
      );
    }
  }
}
