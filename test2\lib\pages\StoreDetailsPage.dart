import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/pages/StoresPage.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/providers/FavoritesProvider.dart';
import 'package:test2/models/Product.dart';
import 'package:test2/widgets/CustomSnackBars.dart';
import 'package:test2/pages/CartPage.dart';
import 'package:test2/utils/DataManager.dart';
import 'package:badges/badges.dart' as badges;

class StoreDetailsPage extends StatefulWidget {
  final Map<String, dynamic> store;

  StoreDetailsPage({required this.store});

  @override
  _StoreDetailsPageState createState() => _StoreDetailsPageState();
}

class _StoreDetailsPageState extends State<StoreDetailsPage> {
  String selectedCategory = "الكل";
  List<Map<String, dynamic>> storeProducts = [];
  List<String> storeCategories = [];

  @override
  void initState() {
    super.initState();
    _loadStoreProducts();
  }

  /// تحميل منتجات المتجر
  void _loadStoreProducts() {
    String storeId = widget.store["id"] ?? "";
    if (storeId.isNotEmpty) {
      storeProducts = DataManager.getItemsByStore(storeId);
      storeCategories = DataManager.getCategoriesForStore(storeId);
    } else {
      // إذا لم يكن هناك معرف، استخدم اسم المتجر
      String storeName = widget.store["name"] ?? "";
      storeProducts = DataManager.getItemsByStoreName(storeName);
      // استخراج الفئات من المنتجات
      Set<String> categories =
          storeProducts.map((item) => item["category"].toString()).toSet();
      storeCategories = ["الكل", ...categories.toList()];
    }
    setState(() {});
  }

  /// فلترة المنتجات حسب الفئة المختارة
  List<Map<String, dynamic>> get filteredProducts {
    if (selectedCategory == "الكل") {
      return storeProducts;
    }
    return storeProducts
        .where((item) => item["category"] == selectedCategory)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(context),
          SliverToBoxAdapter(
            child: _buildStoreInfo(),
          ),
          SliverToBoxAdapter(
            child: _buildCategoriesTab(),
          ),
          _buildProductsGrid(),
        ],
      ),
      floatingActionButton: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          return badges.Badge(
            showBadge: cartProvider.totalQuantity > 0,
            badgeContent: Text(
              cartProvider.totalQuantity > 9
                  ? '9+'
                  : cartProvider.totalQuantity.toString(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            child: FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CartPage()),
                );
              },
              backgroundColor: Color.fromARGB(255, 219, 55, 80),
              child: Icon(
                Icons.shopping_cart,
                color: Color.fromARGB(255, 245, 170, 73),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Color.fromARGB(255, 219, 55, 80),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.store["name"],
          style: TextStyle(color: Colors.white),
        ),
        background: Image.asset(
          widget.store["image"],
          fit: BoxFit.cover,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StoresPage(),
            ),
          );
        },
      ),
      actions: [
        Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            final storeId = widget.store["id"]?.toString() ?? '';
            final isFavorite = favoritesProvider.isStoreFavorite(storeId);
            return IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.red : Colors.white,
              ),
              onPressed: () => _toggleStoreFavorite(storeId, favoritesProvider),
            );
          },
        ),
        IconButton(
          icon: Icon(Icons.search, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildStoreInfo() {
    return Container(
      padding: EdgeInsets.all(15),
      color: Colors.white,
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                textDirection: TextDirection.rtl,
                children: [
                  Icon(Icons.star, color: Colors.amber),
                  SizedBox(width: 5),
                  Text(
                    "${widget.store["rating"]}",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 15),
                  Icon(Icons.access_time, color: Colors.grey),
                  SizedBox(width: 5),
                  Text(
                    widget.store["deliveryTime"],
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  "مفتوح",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Text(
            "عن المتجر",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 5),
          Text(
            textAlign: TextAlign.right,
            "هذا المتجر يقدم مجموعة متنوعة من المنتجات عالية الجودة بأسعار مناسبة. نحن نسعى دائمًا لتقديم أفضل خدمة للعملاء وضمان رضاهم.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 15),
          Row(
            textDirection: TextDirection.rtl,
            children: [
              Icon(Icons.location_on, color: Color(0xFF4C53A5)),
              SizedBox(width: 5),
              Text(
                "شارع الملك فهد، حي الورود، الرياض",
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return Container(
      height: 50,
      margin: EdgeInsets.only(top: 15),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 15),
        itemCount: storeCategories.length,
        itemBuilder: (context, index) {
          String category = storeCategories[index];
          bool isSelected = category == selectedCategory;
          return _buildCategoryTab(category, isSelected);
        },
      ),
    );
  }

  Widget _buildCategoryTab(String title, bool isActive) {
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedCategory = title;
        });
      },
      child: Container(
        margin: EdgeInsets.only(right: 10),
        padding: EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: isActive ? Color(0xFF4C53A5) : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive ? Color(0xFF4C53A5) : Colors.grey,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isActive ? Colors.white : Colors.grey[700],
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء شبكة المنتجات
  Widget _buildProductsGrid() {
    List<Map<String, dynamic>> products = filteredProducts;

    if (products.isEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          height: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory_2_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد منتجات في هذه الفئة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverPadding(
      padding: EdgeInsets.all(15),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.7,
          mainAxisSpacing: 15,
          crossAxisSpacing: 15,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final item = products[index];

            // تحويل البيانات إلى Product مع إضافة الحقول المفقودة ومعلومات المتجر
            final productData = {
              'id': item['id']?.toString() ?? '',
              'name': item['name']?.toString() ?? '',
              'description': item['description']?.toString() ?? '',
              'price': _parsePrice(item['price']),
              'imageUrl': 'images/${item['id']}.png',
              'category': item['category']?.toString() ?? '',
              'storeId': item['storeId']?.toString() ?? '',
              'storeName': item['storeName']?.toString() ?? 'متجر غير محدد',
              'rating': 4.5,
              'reviewCount': 10,
              'isAvailable': true,
              'isFeatured': false,
            };

            return Consumer<CartProvider>(
              builder: (context, cartProvider, child) {
                final productId = productData['id'].toString();
                final isInCart = cartProvider.isProductInCart(productId);
                final quantity = cartProvider.getProductQuantity(productId);

                return Container(
                  padding: EdgeInsets.only(left: 12, right: 12, top: 8),
                  margin: EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                  decoration: BoxDecoration(
                    color: Color(0xFFEDECF2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    textDirection: TextDirection.rtl,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Stack(
                          children: [
                            Center(
                              child: Image.asset(
                                "images/${item['id']}.png",
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[200],
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey[400],
                                      size: 50,
                                    ),
                                  );
                                },
                              ),
                            ),
                            // أيقونة المفضلة
                            Positioned(
                              top: 4,
                              right: 4,
                              child: Consumer<FavoritesProvider>(
                                builder: (context, favoritesProvider, child) {
                                  final productId = productData['id'].toString();
                                  final isFavorite = favoritesProvider.isProductFavorite(productId);
                                  return GestureDetector(
                                    onTap: () => _toggleProductFavorite(productId, favoritesProvider),
                                    child: Container(
                                      padding: EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.9),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        isFavorite ? Icons.favorite : Icons.favorite_border,
                                        color: isFavorite ? Colors.red : Colors.grey,
                                        size: 16,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        productData['name'].toString(),
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF4C53A5),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 3),
                      Text(
                        productData['description'].toString(),
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 6),
                      Row(
                        textDirection: TextDirection.rtl,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${productData['price']} ريال",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF4C53A5),
                            ),
                          ),
                          GestureDetector(
                            onTap: () =>
                                _addToCart(context, productData, cartProvider),
                            child: Container(
                              padding: EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color:
                                    isInCart ? Colors.green : Color(0xFF4C53A5),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    isInCart ? Icons.check : Icons.add_circle,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  if (isInCart && quantity > 0) ...[
                                    SizedBox(width: 4),
                                    Text(
                                      quantity.toString(),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                    ],
                  ),
                );
              },
            );
          },
          childCount: products.length,
        ),
      ),
    );
  }

  /// تحليل السعر من النص
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  /// إضافة المنتج إلى السلة
  void _addToCart(BuildContext context, Map<String, dynamic> productData,
      CartProvider cartProvider) {
    try {
      final product = Product.fromMap(productData);

      cartProvider.addToCart(product).then((success) {
        if (success) {
          CustomSnackBars.showSuccess(
            context,
            message: 'تم إضافة ${product.name} إلى السلة',
            subtitle: 'يمكنك مراجعة سلتك من الأسفل',
          );
        } else {
          CustomSnackBars.showError(
            context,
            message: 'فشل في إضافة المنتج',
            subtitle: 'يرجى المحاولة مرة أخرى',
          );
        }
      });
    } catch (e) {
      CustomSnackBars.showError(
        context,
        message: 'خطأ في إضافة المنتج',
        subtitle: e.toString(),
      );
    }
  }

  /// تبديل حالة المفضلة للمحل
  void _toggleStoreFavorite(String storeId, FavoritesProvider favoritesProvider) {
    favoritesProvider.toggleStoreFavorite(storeId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isStoreFavorite(storeId);
        CustomSnackBars.showSuccess(
          context,
          message: isFavorite ? 'تم إضافة المحل للمفضلة' : 'تم إزالة المحل من المفضلة',
          subtitle: isFavorite ? 'يمكنك العثور عليه في صفحة المفضلات' : '',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في تحديث المفضلة',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }

  /// تبديل حالة المفضلة للمنتج
  void _toggleProductFavorite(String productId, FavoritesProvider favoritesProvider) {
    favoritesProvider.toggleProductFavorite(productId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isProductFavorite(productId);
        CustomSnackBars.showSuccess(
          context,
          message: isFavorite ? 'تم إضافة المنتج للمفضلة' : 'تم إزالة المنتج من المفضلة',
          subtitle: isFavorite ? 'يمكنك العثور عليه في صفحة المفضلات' : '',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في تحديث المفضلة',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }
}
