2025/06/22-16:07:57.386 4150 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.de4161f3\flutter_tools_chrome_device.3a885c7c\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/06/22-16:07:57.386 4150 Recovering log #14
2025/06/22-16:07:57.386 4150 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.de4161f3\flutter_tools_chrome_device.3a885c7c\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000014.log 
2025/06/22-16:07:57.386 4150 Delete type=2 #12
2025/06/22-16:07:57.390 24d0 Level-0 table #19: started
2025/06/22-16:07:57.391 24d0 Level-0 table #19: 1416 bytes OK
2025/06/22-16:07:57.392 24d0 Delete type=0 #14
2025/06/22-16:07:57.392 24d0 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x07\x00\x00\x05' @ 243 : 1
2025/06/22-16:07:57.392 24d0 Compacting 1@0 + 1@1 files
2025/06/22-16:07:57.393 24d0 Generated table #20@0: 50 keys, 1431 bytes
2025/06/22-16:07:57.393 24d0 Compacted 1@0 + 1@1 files => 1431 bytes
2025/06/22-16:07:57.393 24d0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/22-16:07:57.394 24d0 Delete type=2 #19
2025/06/22-16:07:57.394 24d0 Manual compaction at level-0 from '\x00\x07\x00\x00\x05' @ 243 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
