2025/06/22-16:24:44.012 4c98 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cea6c73c\flutter_tools_chrome_device.406b0a63\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/06/22-16:24:44.012 4c98 Recovering log #18
2025/06/22-16:24:44.012 4c98 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cea6c73c\flutter_tools_chrome_device.406b0a63\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000018.log 
2025/06/22-16:24:44.012 4c98 Delete type=0 #4
2025/06/22-16:24:44.012 4c98 Delete type=0 #7
2025/06/22-16:24:44.012 4c98 Delete type=2 #8
2025/06/22-16:24:44.013 4c98 Delete type=0 #10
2025/06/22-16:24:44.013 4c98 Delete type=2 #12
2025/06/22-16:24:44.013 4c98 Delete type=2 #16
2025/06/22-16:24:44.018 5610 Level-0 table #23: started
2025/06/22-16:24:44.019 5610 Level-0 table #23: 916 bytes OK
2025/06/22-16:24:44.020 5610 Delete type=0 #18
2025/06/22-16:24:44.020 117c Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x08\x00\x00\x05' @ 276 : 1
2025/06/22-16:24:44.020 117c Compacting 1@0 + 1@1 files
2025/06/22-16:24:44.021 117c Generated table #24@0: 50 keys, 1435 bytes
2025/06/22-16:24:44.022 117c Compacted 1@0 + 1@1 files => 1435 bytes
2025/06/22-16:24:44.022 117c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/22-16:24:44.022 117c Delete type=2 #23
2025/06/22-16:24:44.023 117c Manual compaction at level-0 from '\x00\x08\x00\x00\x05' @ 276 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
