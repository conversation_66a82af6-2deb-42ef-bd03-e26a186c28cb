2025/06/22-16:36:33.688 27d8 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.232456ea\flutter_tools_chrome_device.ae4ee79f\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/06/22-16:36:33.689 27d8 Recovering log #26
2025/06/22-16:36:33.689 27d8 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.232456ea\flutter_tools_chrome_device.ae4ee79f\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000026.log 
2025/06/22-16:36:33.689 27d8 Delete type=0 #4
2025/06/22-16:36:33.689 27d8 Delete type=0 #7
2025/06/22-16:36:33.689 27d8 Delete type=2 #8
2025/06/22-16:36:33.689 27d8 Delete type=0 #10
2025/06/22-16:36:33.689 27d8 Delete type=2 #12
2025/06/22-16:36:33.689 27d8 Delete type=2 #16
2025/06/22-16:36:33.690 27d8 Delete type=0 #18
2025/06/22-16:36:33.690 27d8 Delete type=2 #20
2025/06/22-16:36:33.690 27d8 Delete type=0 #22
2025/06/22-16:36:33.690 27d8 Delete type=2 #24
2025/06/22-16:36:33.696 2590 Level-0 table #31: started
2025/06/22-16:36:33.697 2590 Level-0 table #31: 902 bytes OK
2025/06/22-16:36:33.698 2590 Delete type=0 #26
2025/06/22-16:36:33.698 4cd0 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 342 : 1
2025/06/22-16:36:33.698 4cd0 Compacting 1@0 + 1@1 files
2025/06/22-16:36:33.699 4cd0 Generated table #32@0: 50 keys, 1436 bytes
2025/06/22-16:36:33.699 4cd0 Compacted 1@0 + 1@1 files => 1436 bytes
2025/06/22-16:36:33.700 4cd0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/22-16:36:33.700 4cd0 Delete type=2 #31
2025/06/22-16:36:33.700 4cd0 Manual compaction at level-0 from '\x00\x0a\x00\x00\x05' @ 342 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
