2025/06/22-18:42:24.870 346c Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.b4b2527a\flutter_tools_chrome_device.dcbf609f\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/06/22-18:42:24.870 346c Recovering log #38
2025/06/22-18:42:24.870 346c Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.b4b2527a\flutter_tools_chrome_device.dcbf609f\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000038.log 
2025/06/22-18:42:24.871 346c Delete type=0 #4
2025/06/22-18:42:24.871 346c Delete type=0 #7
2025/06/22-18:42:24.871 346c Delete type=2 #8
2025/06/22-18:42:24.871 346c Delete type=0 #10
2025/06/22-18:42:24.871 346c Delete type=2 #12
2025/06/22-18:42:24.871 346c Delete type=2 #16
2025/06/22-18:42:24.871 346c Delete type=0 #18
2025/06/22-18:42:24.871 346c Delete type=2 #20
2025/06/22-18:42:24.871 346c Delete type=0 #22
2025/06/22-18:42:24.871 346c Delete type=2 #24
2025/06/22-18:42:24.871 346c Delete type=0 #26
2025/06/22-18:42:24.871 346c Delete type=2 #28
2025/06/22-18:42:24.871 346c Delete type=0 #30
2025/06/22-18:42:24.871 346c Delete type=2 #32
2025/06/22-18:42:24.871 346c Delete type=0 #34
2025/06/22-18:42:24.871 346c Delete type=2 #36
2025/06/22-18:42:24.879 3258 Level-0 table #43: started
2025/06/22-18:42:24.880 3258 Level-0 table #43: 924 bytes OK
2025/06/22-18:42:24.881 3258 Delete type=0 #38
2025/06/22-18:42:24.881 346c Manual compaction at level-0 from '\x00\x0e\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0f\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x00\x00\x05' @ 474 : 1
2025/06/22-18:42:24.881 346c Compacting 1@0 + 1@1 files
2025/06/22-18:42:24.882 346c Generated table #44@0: 50 keys, 1434 bytes
2025/06/22-18:42:24.882 346c Compacted 1@0 + 1@1 files => 1434 bytes
2025/06/22-18:42:24.883 346c compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/22-18:42:24.883 346c Delete type=2 #43
2025/06/22-18:42:24.883 346c Manual compaction at level-0 from '\x00\x0e\x00\x00\x05' @ 474 : 1 .. '\x00\x0f\x00\x00\x00' @ 0 : 0; will stop at (end)
