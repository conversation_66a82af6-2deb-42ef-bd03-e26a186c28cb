import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/Product.dart';

/// خدمة Firebase للمنتجات
class FirebaseProductService {
  static final FirebaseProductService _instance = FirebaseProductService._internal();
  factory FirebaseProductService() => _instance;
  FirebaseProductService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String productsCollection = 'products';

  /// جلب جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    try {
      print('بدء جلب جميع المنتجات من Firebase...');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} منتج من Firebase');
      return products;
    } catch (e) {
      print('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// جلب المنتجات المفعلة فقط
  Future<List<Product>> getEnabledProducts() async {
    try {
      print('بدء جلب المنتجات المفعلة من Firebase...');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .where('enable', isEqualTo: true)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} منتج مفعل من Firebase');
      return products;
    } catch (e) {
      print('خطأ في جلب المنتجات المفعلة: $e');
      return [];
    }
  }

  /// جلب المنتجات حسب المتجر
  Future<List<Product>> getProductsByStore(String storeId) async {
    try {
      print('بدء جلب منتجات المتجر: $storeId');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .where('store_id', isEqualTo: storeId)
          .where('enable', isEqualTo: true)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} منتج للمتجر: $storeId');
      return products;
    } catch (e) {
      print('خطأ في جلب منتجات المتجر: $e');
      return [];
    }
  }

  /// جلب المنتجات حسب الفئة
  Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      print('بدء جلب منتجات الفئة: $categoryId');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .where('categ_id', isEqualTo: categoryId)
          .where('enable', isEqualTo: true)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} منتج للفئة: $categoryId');
      return products;
    } catch (e) {
      print('خطأ في جلب منتجات الفئة: $e');
      return [];
    }
  }

  /// جلب المنتجات المميزة (التي تظهر في الشاشة الرئيسية)
  Future<List<Product>> getFeaturedProducts() async {
    try {
      print('بدء جلب المنتجات المميزة من Firebase...');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .where('is_static_in_main_screen', isEqualTo: true)
          .where('enable', isEqualTo: true)
          .limit(10)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} منتج مميز من Firebase');
      return products;
    } catch (e) {
      print('خطأ في جلب المنتجات المميزة: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts(String searchTerm) async {
    try {
      print('بدء البحث عن: $searchTerm');
      
      // البحث في اسم المنتج
      final nameQuery = await _firestore
          .collection(productsCollection)
          .where('product_name', isGreaterThanOrEqualTo: searchTerm)
          .where('product_name', isLessThanOrEqualTo: searchTerm + '\uf8ff')
          .where('enable', isEqualTo: true)
          .get();

      // البحث في وصف المنتج
      final descQuery = await _firestore
          .collection(productsCollection)
          .where('product_description', isGreaterThanOrEqualTo: searchTerm)
          .where('product_description', isLessThanOrEqualTo: searchTerm + '\uf8ff')
          .where('enable', isEqualTo: true)
          .get();

      // دمج النتائج وإزالة المكررات
      final Set<String> seenIds = {};
      final List<Product> products = [];

      for (final doc in [...nameQuery.docs, ...descQuery.docs]) {
        if (!seenIds.contains(doc.id)) {
          seenIds.add(doc.id);
          products.add(Product.fromFirestore(doc.data(), doc.id));
        }
      }

      print('تم العثور على ${products.length} منتج للبحث: $searchTerm');
      return products;
    } catch (e) {
      print('خطأ في البحث عن المنتجات: $e');
      return [];
    }
  }

  /// جلب منتج واحد حسب المعرف
  Future<Product?> getProductById(String productId) async {
    try {
      print('بدء جلب المنتج: $productId');
      
      final doc = await _firestore
          .collection(productsCollection)
          .doc(productId)
          .get();

      if (doc.exists && doc.data() != null) {
        final product = Product.fromFirestore(doc.data()!, doc.id);
        print('تم جلب المنتج: ${product.name}');
        return product;
      } else {
        print('المنتج غير موجود: $productId');
        return null;
      }
    } catch (e) {
      print('خطأ في جلب المنتج: $e');
      return null;
    }
  }

  /// تحديث عدد المشاهدات للمنتج
  Future<void> incrementProductViews(String productId) async {
    try {
      await _firestore
          .collection(productsCollection)
          .doc(productId)
          .update({
        'views': FieldValue.increment(1),
        'update_date': DateTime.now().toIso8601String(),
      });
      
      print('تم تحديث عدد المشاهدات للمنتج: $productId');
    } catch (e) {
      print('خطأ في تحديث عدد المشاهدات: $e');
    }
  }

  /// جلب المنتجات مع الاستماع للتحديثات المباشرة
  Stream<List<Product>> getProductsStream() {
    return _firestore
        .collection(productsCollection)
        .where('enable', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();
    });
  }

  /// جلب المنتجات حسب المتجر مع الاستماع للتحديثات
  Stream<List<Product>> getProductsByStoreStream(String storeId) {
    return _firestore
        .collection(productsCollection)
        .where('store_id', isEqualTo: storeId)
        .where('enable', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();
    });
  }

  /// جلب المنتجات مرتبة حسب الأحدث
  Future<List<Product>> getLatestProducts({int limit = 20}) async {
    try {
      print('بدء جلب أحدث المنتجات...');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .where('enable', isEqualTo: true)
          .orderBy('datetimes', descending: true)
          .limit(limit)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} من أحدث المنتجات');
      return products;
    } catch (e) {
      print('خطأ في جلب أحدث المنتجات: $e');
      return [];
    }
  }

  /// جلب المنتجات الأكثر مشاهدة
  Future<List<Product>> getMostViewedProducts({int limit = 20}) async {
    try {
      print('بدء جلب المنتجات الأكثر مشاهدة...');
      
      final querySnapshot = await _firestore
          .collection(productsCollection)
          .where('enable', isEqualTo: true)
          .orderBy('views', descending: true)
          .limit(limit)
          .get();

      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc.data(), doc.id))
          .toList();

      print('تم جلب ${products.length} من المنتجات الأكثر مشاهدة');
      return products;
    } catch (e) {
      print('خطأ في جلب المنتجات الأكثر مشاهدة: $e');
      return [];
    }
  }
}
