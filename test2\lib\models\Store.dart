import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج المتجر
class Store {
  final String id;
  final String name;
  final String description;
  final String category;
  final double rating;
  final String image;
  final bool isOpen;
  final String? address;
  final String? phone;
  final List<String> tags;
  final double? deliveryFee;
  final int? deliveryTime; // بالدقائق
  final double? minimumOrder;
  final DateTime? openTime;
  final DateTime? closeTime;
  final int reviewCount;
  final bool isVerified;
  final bool isFeatured;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Store({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.rating,
    required this.image,
    required this.isOpen,
    this.address,
    this.phone,
    this.tags = const [],
    this.deliveryFee,
    this.deliveryTime,
    this.minimumOrder,
    this.openTime,
    this.closeTime,
    this.reviewCount = 0,
    this.isVerified = false,
    this.isFeatured = false,
    this.createdAt,
    this.updatedAt,
  });

  /// تحويل من JSON
  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      image: json['image'] ?? '',
      isOpen: json['isOpen'] ?? false,
      address: json['address'],
      phone: json['phone'],
      tags: List<String>.from(json['tags'] ?? []),
      deliveryFee: json['deliveryFee']?.toDouble(),
      deliveryTime: json['deliveryTime'],
      minimumOrder: json['minimumOrder']?.toDouble(),
      openTime:
          json['openTime'] != null ? DateTime.parse(json['openTime']) : null,
      closeTime:
          json['closeTime'] != null ? DateTime.parse(json['closeTime']) : null,
      reviewCount: json['reviewCount'] ?? 0,
      isVerified: json['isVerified'] ?? false,
      isFeatured: json['isFeatured'] ?? false,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  /// تحويل من Firestore Document
  factory Store.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // تحويل deliveryTime من string إلى int
    int parseDeliveryTime(dynamic deliveryTime) {
      if (deliveryTime is int) return deliveryTime;
      if (deliveryTime is String) {
        // استخراج الرقم الأول من النص مثل "20-30 دقيقة" -> 20
        final match = RegExp(r'(\d+)').firstMatch(deliveryTime);
        return match != null ? int.parse(match.group(1)!) : 30;
      }
      return 30; // قيمة افتراضية
    }

    return Store(
      id: doc.id,
      // استخدام أسماء الحقول الصحيحة من Firebase
      name: data['store_name'] ?? data['name'] ?? '',
      description: data['description'] ?? 'متجر رائع يقدم أفضل الخدمات',
      category: data['store_categ'] ?? data['category'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      image: data['image'] ?? 'images/default_store.png',
      isOpen: data['isOpen'] ?? true,
      address: data['store_location'] ?? data['address'] ?? '',
      phone: data['phone'] ?? '',
      tags: List<String>.from(data['tags'] ?? []),
      deliveryFee: data['deliveryFee']?.toDouble(),
      deliveryTime: parseDeliveryTime(data['deliveryTime']),
      minimumOrder: data['minimumOrder']?.toDouble(),
      openTime: data['openTime'] != null
          ? (data['openTime'] as Timestamp).toDate()
          : null,
      closeTime: data['closeTime'] != null
          ? (data['closeTime'] as Timestamp).toDate()
          : null,
      reviewCount: data['reviewCount'] ?? 0,
      isVerified: data['isVerified'] ?? false,
      isFeatured: data['isFeatured'] ?? false,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'rating': rating,
      'image': image,
      'isOpen': isOpen,
      'address': address,
      'phone': phone,
      'tags': tags,
      'deliveryFee': deliveryFee,
      'deliveryTime': deliveryTime,
      'minimumOrder': minimumOrder,
      'openTime': openTime?.toIso8601String(),
      'closeTime': closeTime?.toIso8601String(),
      'reviewCount': reviewCount,
      'isVerified': isVerified,
      'isFeatured': isFeatured,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// تحويل إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'category': category,
      'rating': rating,
      'image': image,
      'isOpen': isOpen,
      'address': address,
      'phone': phone,
      'tags': tags,
      'deliveryFee': deliveryFee,
      'deliveryTime': deliveryTime,
      'minimumOrder': minimumOrder,
      'openTime': openTime != null ? Timestamp.fromDate(openTime!) : null,
      'closeTime': closeTime != null ? Timestamp.fromDate(closeTime!) : null,
      'reviewCount': reviewCount,
      'isVerified': isVerified,
      'isFeatured': isFeatured,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// نسخ مع تعديل
  Store copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    double? rating,
    String? image,
    bool? isOpen,
    String? address,
    String? phone,
    List<String>? tags,
    double? deliveryFee,
    int? deliveryTime,
    double? minimumOrder,
    DateTime? openTime,
    DateTime? closeTime,
    int? reviewCount,
    bool? isVerified,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Store(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      image: image ?? this.image,
      isOpen: isOpen ?? this.isOpen,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      tags: tags ?? this.tags,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      deliveryTime: deliveryTime ?? this.deliveryTime,
      minimumOrder: minimumOrder ?? this.minimumOrder,
      openTime: openTime ?? this.openTime,
      closeTime: closeTime ?? this.closeTime,
      reviewCount: reviewCount ?? this.reviewCount,
      isVerified: isVerified ?? this.isVerified,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Store && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Store(id: $id, name: $name, category: $category, rating: $rating, isOpen: $isOpen)';
  }

  /// الحصول على نص حالة المتجر
  String get statusText {
    if (isOpen) {
      return 'مفتوح';
    } else {
      return 'مغلق';
    }
  }

  /// الحصول على لون حالة المتجر
  String get statusColor {
    if (isOpen) {
      return '#4CAF50'; // أخضر
    } else {
      return '#F44336'; // أحمر
    }
  }

  /// الحصول على نص رسوم التوصيل
  String get deliveryFeeText {
    if (deliveryFee == null) {
      return 'غير محدد';
    } else if (deliveryFee == 0) {
      return 'توصيل مجاني';
    } else {
      return '${deliveryFee!.toStringAsFixed(0)} ريال';
    }
  }

  /// الحصول على نص وقت التوصيل
  String get deliveryTimeText {
    if (deliveryTime == null) {
      return 'غير محدد';
    } else {
      return '$deliveryTime دقيقة';
    }
  }

  /// الحصول على نص الحد الأدنى للطلب
  String get minimumOrderText {
    if (minimumOrder == null) {
      return 'لا يوجد حد أدنى';
    } else {
      return 'الحد الأدنى: ${minimumOrder!.toStringAsFixed(0)} ريال';
    }
  }

  /// التحقق من كون المتجر مفتوح حالياً
  bool get isCurrentlyOpen {
    if (!isOpen) return false;

    final now = DateTime.now();
    final currentTime = TimeOfDay.fromDateTime(now);

    if (openTime != null && closeTime != null) {
      final openTimeOfDay = TimeOfDay.fromDateTime(openTime!);
      final closeTimeOfDay = TimeOfDay.fromDateTime(closeTime!);

      // التحقق من كون الوقت الحالي بين وقت الفتح والإغلاق
      final nowMinutes = currentTime.hour * 60 + currentTime.minute;
      final openMinutes = openTimeOfDay.hour * 60 + openTimeOfDay.minute;
      final closeMinutes = closeTimeOfDay.hour * 60 + closeTimeOfDay.minute;

      if (closeMinutes > openMinutes) {
        // نفس اليوم
        return nowMinutes >= openMinutes && nowMinutes <= closeMinutes;
      } else {
        // يمتد للليل (مثل 10 مساءً إلى 2 صباحاً)
        return nowMinutes >= openMinutes || nowMinutes <= closeMinutes;
      }
    }

    return isOpen;
  }

  /// الحصول على نص أوقات العمل
  String get workingHoursText {
    if (openTime != null && closeTime != null) {
      final openTimeStr = TimeOfDay.fromDateTime(openTime!).formatTime();
      final closeTimeStr = TimeOfDay.fromDateTime(closeTime!).formatTime();
      return '$openTimeStr - $closeTimeStr';
    }
    return 'غير محدد';
  }
}

/// فئة مساعدة لـ TimeOfDay
extension TimeOfDayExtension on TimeOfDay {
  String formatTime() {
    final hour = this.hour.toString().padLeft(2, '0');
    final minute = this.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
