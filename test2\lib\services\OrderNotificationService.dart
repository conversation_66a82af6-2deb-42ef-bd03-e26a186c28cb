import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/Order.dart' as AppOrder;
import '../models/Notification.dart';
import '../services/NotificationService.dart';
import '../services/CustomerDataService.dart';
import '../services/FirebaseMessagingService.dart';

/// خدمة مراقبة تحديثات الطلبات وإرسال الإشعارات
class OrderNotificationService {
  static final OrderNotificationService _instance = OrderNotificationService._internal();
  factory OrderNotificationService() => _instance;
  OrderNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final NotificationService _notificationService = NotificationService();
  final CustomerDataService _customerService = CustomerDataService();
  final FirebaseMessagingService _messagingService = FirebaseMessagingService();

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  final Map<String, AppOrder.OrderStatus> _lastKnownStatuses = {};
  bool _isInitialized = false;
  bool _isOnline = true;

  /// تهيئة خدمة مراقبة الطلبات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _customerService.ensureDataLoaded();
      final userId = _customerService.getCustomerId();

      if (userId.isNotEmpty) {
        // تهيئة Firebase Messaging
        await _messagingService.initialize();

        // بدء مراقبة الاتصال
        _startConnectivityMonitoring();

        // بدء مراقبة الطلبات
        await _startListeningToUserOrders(userId);
        _isInitialized = true;
        print('تم تهيئة خدمة مراقبة الطلبات للمستخدم: $userId');
      }
    } catch (e) {
      print('خطأ في تهيئة خدمة مراقبة الطلبات: $e');
    }
  }

  /// بدء مراقبة حالة الاتصال
  void _startConnectivityMonitoring() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (ConnectivityResult result) {
        final wasOnline = _isOnline;
        _isOnline = result != ConnectivityResult.none;

        print('حالة الاتصال: ${_isOnline ? "متصل" : "غير متصل"}');

        // إذا عاد الاتصال بعد انقطاع، معالجة الإشعارات المؤجلة
        if (!wasOnline && _isOnline) {
          _handleReconnection();
        }
      },
    );
  }

  /// معالجة عودة الاتصال
  Future<void> _handleReconnection() async {
    print('تم استعادة الاتصال - معالجة الإشعارات المؤجلة');
    try {
      // معالجة الإشعارات المؤجلة في Firebase Messaging
      await _messagingService.initialize();

      // إعادة تحميل حالات الطلبات الحالية
      final userId = _customerService.getCustomerId();
      if (userId.isNotEmpty) {
        await _loadCurrentOrderStatuses(userId);
      }

      print('تم معالجة الإشعارات المؤجلة بنجاح');
    } catch (e) {
      print('خطأ في معالجة الإشعارات المؤجلة: $e');
    }
  }

  /// بدء مراقبة طلبات المستخدم
  Future<void> _startListeningToUserOrders(String userId) async {
    try {
      // إلغاء أي اشتراك سابق
      await stopListening();

      // بدء مراقبة طلبات المستخدم
      _ordersSubscription = _firestore
          .collection('orders')
          .where('userId', isEqualTo: userId)
          .snapshots()
          .listen(
        _handleOrdersUpdate,
        onError: (error) {
          print('خطأ في مراقبة الطلبات: $error');
        },
      );

      print('بدأت مراقبة طلبات المستخدم: $userId');
    } catch (e) {
      print('خطأ في بدء مراقبة الطلبات: $e');
    }
  }

  /// معالجة تحديثات الطلبات
  void _handleOrdersUpdate(QuerySnapshot snapshot) {
    try {
      for (var change in snapshot.docChanges) {
        final data = change.doc.data() as Map<String, dynamic>?;
        if (data == null) continue;

        final orderId = data['id'] ?? change.doc.id;
        final currentStatus = _parseOrderStatus(data['status']);
        final lastKnownStatus = _lastKnownStatuses[orderId];

        // التحقق من وجود تحديث في الحالة
        if (lastKnownStatus != null && lastKnownStatus != currentStatus) {
          _sendOrderStatusNotification(orderId, currentStatus, data);
        }

        // تحديث الحالة المعروفة
        _lastKnownStatuses[orderId] = currentStatus;
      }
    } catch (e) {
      print('خطأ في معالجة تحديثات الطلبات: $e');
    }
  }

  /// إرسال إشعار تحديث حالة الطلب
  Future<void> _sendOrderStatusNotification(
    String orderId,
    AppOrder.OrderStatus status,
    Map<String, dynamic> orderData,
  ) async {
    try {
      final storeName = orderData['storeName'] ?? 'المتجر';
      final statusMessage = _getStatusNotificationMessage(status, storeName);
      final statusIcon = _getStatusIcon(status);
      final notificationType = _getNotificationType(status);

      final notificationData = {
        'id': 'order_${orderId}_${DateTime.now().millisecondsSinceEpoch}',
        'title': '$statusIcon تحديث طلبك #${orderId.substring(orderId.length - 6)}',
        'message': statusMessage,
        'type': notificationType.toString().split('.').last,
        'orderId': orderId,
        'status': status.toString().split('.').last,
        'storeName': storeName,
        'action': 'order_status_update',
        'timestamp': DateTime.now().toIso8601String(),
      };

      // إذا كان متصل بالإنترنت، إرسال الإشعار مباشرة
      if (_isOnline) {
        await _notificationService.addNotification(
          title: notificationData['title']!,
          message: notificationData['message']!,
          type: notificationType,
          data: {
            'orderId': orderId,
            'status': status.toString().split('.').last,
            'storeName': storeName,
            'action': 'order_status_update',
          },
        );
        print('تم إرسال إشعار تحديث الطلب: $orderId -> $status');
      } else {
        // إذا كان غير متصل، إضافة الإشعار إلى قائمة الانتظار
        await _messagingService.queueNotificationForLater(notificationData);
        print('تم إضافة إشعار إلى قائمة الانتظار: $orderId -> $status');
      }
    } catch (e) {
      print('خطأ في إرسال إشعار الطلب: $e');
    }
  }

  /// الحصول على نوع الإشعار حسب حالة الطلب
  NotificationType _getNotificationType(AppOrder.OrderStatus status) {
    switch (status) {
      case AppOrder.OrderStatus.confirmed:
        return NotificationType.orderConfirmed;
      case AppOrder.OrderStatus.preparing:
        return NotificationType.orderPreparing;
      case AppOrder.OrderStatus.ready:
        return NotificationType.orderReady;
      case AppOrder.OrderStatus.delivered:
        return NotificationType.orderDelivered;
      default:
        return NotificationType.general;
    }
  }

  /// الحصول على رسالة الإشعار حسب الحالة
  String _getStatusNotificationMessage(AppOrder.OrderStatus status, String storeName) {
    switch (status) {
      case AppOrder.OrderStatus.confirmed:
        return 'تم تأكيد طلبك من $storeName وبدء التجهيز';
      case AppOrder.OrderStatus.preparing:
        return 'يتم تجهيز طلبك الآن في $storeName';
      case AppOrder.OrderStatus.ready:
        return 'طلبك جاهز وفي انتظار عامل التوصيل';
      case AppOrder.OrderStatus.pickedUp:
        return 'تم استلام طلبك من عامل التوصيل';
      case AppOrder.OrderStatus.onTheWay:
        return 'عامل التوصيل في الطريق إليك';
      case AppOrder.OrderStatus.delivered:
        return 'تم توصيل طلبك بنجاح! نتمنى أن تكون راضياً عن الخدمة';
      case AppOrder.OrderStatus.cancelled:
        return 'تم إلغاء طلبك. سيتم استرداد المبلغ خلال 3-5 أيام عمل';
      default:
        return 'تم تحديث حالة طلبك';
    }
  }

  /// الحصول على أيقونة الحالة
  String _getStatusIcon(AppOrder.OrderStatus status) {
    switch (status) {
      case AppOrder.OrderStatus.confirmed:
        return '✅';
      case AppOrder.OrderStatus.preparing:
        return '👨‍🍳';
      case AppOrder.OrderStatus.ready:
        return '📦';
      case AppOrder.OrderStatus.pickedUp:
        return '🚗';
      case AppOrder.OrderStatus.onTheWay:
        return '🛣️';
      case AppOrder.OrderStatus.delivered:
        return '🎉';
      case AppOrder.OrderStatus.cancelled:
        return '❌';
      default:
        return '📋';
    }
  }

  /// تحليل حالة الطلب من النص
  AppOrder.OrderStatus _parseOrderStatus(String? status) {
    if (status == null) return AppOrder.OrderStatus.pending;
    
    return AppOrder.OrderStatus.values.firstWhere(
      (e) => e.toString().split('.').last == status,
      orElse: () => AppOrder.OrderStatus.pending,
    );
  }

  /// تحديث الحالات المعروفة للطلبات الحالية
  Future<void> _loadCurrentOrderStatuses(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('userId', isEqualTo: userId)
          .get();

      for (var doc in snapshot.docs) {
        final data = doc.data();
        final orderId = data['id'] ?? doc.id;
        final status = _parseOrderStatus(data['status']);
        _lastKnownStatuses[orderId] = status;
      }

      print('تم تحميل ${_lastKnownStatuses.length} حالة طلب للمستخدم');
    } catch (e) {
      print('خطأ في تحميل حالات الطلبات: $e');
    }
  }

  /// إيقاف مراقبة الطلبات
  Future<void> stopListening() async {
    await _ordersSubscription?.cancel();
    _ordersSubscription = null;
    print('تم إيقاف مراقبة الطلبات');
  }

  /// إعادة تهيئة الخدمة للمستخدم الجديد
  Future<void> reinitializeForUser(String userId) async {
    _lastKnownStatuses.clear();
    _isInitialized = false;
    await stopListening();
    await _loadCurrentOrderStatuses(userId);
    await _startListeningToUserOrders(userId);
    _isInitialized = true;
    print('تم إعادة تهيئة خدمة مراقبة الطلبات للمستخدم: $userId');
  }

  /// تنظيف الموارد
  void dispose() {
    stopListening();
    _lastKnownStatuses.clear();
    _isInitialized = false;
  }
}
