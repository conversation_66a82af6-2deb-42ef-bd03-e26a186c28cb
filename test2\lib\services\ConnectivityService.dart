import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

/// خدمة فحص ومراقبة الاتصال بالإنترنت
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  bool _isConnected = false;
  String _connectionType = 'none';
  final List<Function(bool)> _listeners = [];

  /// الحصول على حالة الاتصال الحالية
  bool get isConnected => _isConnected;
  
  /// الحصول على نوع الاتصال
  String get connectionType => _connectionType;

  /// تهيئة خدمة الاتصال
  Future<void> initialize() async {
    try {
      // فحص الاتصال الأولي
      await _checkInitialConnection();
      
      // بدء مراقبة تغييرات الاتصال
      _startListening();
      
      print('تم تهيئة خدمة الاتصال - الحالة: ${_isConnected ? "متصل" : "غير متصل"}');
    } catch (e) {
      print('خطأ في تهيئة خدمة الاتصال: $e');
    }
  }

  /// فحص الاتصال الأولي
  Future<void> _checkInitialConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(connectivityResult);
    } catch (e) {
      print('خطأ في فحص الاتصال الأولي: $e');
      _isConnected = false;
      _connectionType = 'error';
    }
  }

  /// بدء مراقبة تغييرات الاتصال
  void _startListening() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
      onError: (error) {
        print('خطأ في مراقبة الاتصال: $error');
        _isConnected = false;
        _connectionType = 'error';
        _notifyListeners();
      },
    );
  }

  /// تحديث حالة الاتصال
  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    final wasConnected = _isConnected;

    // تحديد نوع الاتصال
    if (result == ConnectivityResult.wifi) {
      _connectionType = 'wifi';
      _isConnected = await _verifyInternetConnection();
    } else if (result == ConnectivityResult.mobile) {
      _connectionType = 'mobile';
      _isConnected = await _verifyInternetConnection();
    } else if (result == ConnectivityResult.ethernet) {
      _connectionType = 'ethernet';
      _isConnected = await _verifyInternetConnection();
    } else {
      _connectionType = 'none';
      _isConnected = false;
    }

    // إشعار المستمعين إذا تغيرت الحالة
    if (wasConnected != _isConnected) {
      print('تغيرت حالة الاتصال: ${_isConnected ? "متصل" : "غير متصل"} ($connectionType)');
      _notifyListeners();
    }
  }

  /// التحقق الفعلي من الاتصال بالإنترنت
  Future<bool> _verifyInternetConnection() async {
    try {
      // في بيئة الويب، نفترض أن الاتصال متاح إذا كان هناك اتصال شبكة
      if (kIsWeb) {
        return true;
      }
      
      // للمنصات الأخرى، نحاول الاتصال بخادم Google
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      print('فشل في التحقق من الاتصال بالإنترنت: $e');
      return false;
    }
  }

  /// إضافة مستمع لتغييرات الاتصال
  void addListener(Function(bool isConnected) listener) {
    _listeners.add(listener);
  }

  /// إزالة مستمع
  void removeListener(Function(bool isConnected) listener) {
    _listeners.remove(listener);
  }

  /// إشعار جميع المستمعين
  void _notifyListeners() {
    for (final listener in _listeners) {
      try {
        listener(_isConnected);
      } catch (e) {
        print('خطأ في إشعار مستمع الاتصال: $e');
      }
    }
  }

  /// فحص الاتصال يدوياً
  Future<bool> checkConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(connectivityResult);
      return _isConnected;
    } catch (e) {
      print('خطأ في فحص الاتصال اليدوي: $e');
      return false;
    }
  }

  /// الحصول على معلومات مفصلة عن الاتصال
  Map<String, dynamic> getConnectionInfo() {
    return {
      'isConnected': _isConnected,
      'connectionType': _connectionType,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// الحصول على رسالة حالة الاتصال
  String getConnectionStatusMessage() {
    if (!_isConnected) {
      return 'غير متصل بالإنترنت';
    }
    
    switch (_connectionType) {
      case 'wifi':
        return 'متصل عبر WiFi';
      case 'mobile':
        return 'متصل عبر بيانات الجوال';
      case 'ethernet':
        return 'متصل عبر Ethernet';
      default:
        return 'متصل بالإنترنت';
    }
  }

  /// الحصول على أيقونة حالة الاتصال
  String getConnectionIcon() {
    if (!_isConnected) {
      return '📵';
    }
    
    switch (_connectionType) {
      case 'wifi':
        return '📶';
      case 'mobile':
        return '📱';
      case 'ethernet':
        return '🌐';
      default:
        return '🔗';
    }
  }

  /// انتظار الاتصال (مفيد للتطبيقات التي تحتاج انتظار الاتصال)
  Future<bool> waitForConnection({Duration timeout = const Duration(minutes: 5)}) async {
    if (_isConnected) return true;
    
    final completer = Completer<bool>();
    late Function(bool) listener;
    
    listener = (bool isConnected) {
      if (isConnected) {
        removeListener(listener);
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    };
    
    addListener(listener);
    
    // إضافة timeout
    Timer(timeout, () {
      removeListener(listener);
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });
    
    return completer.future;
  }

  /// إيقاف خدمة الاتصال
  void dispose() {
    _connectivitySubscription?.cancel();
    _listeners.clear();
    print('تم إيقاف خدمة الاتصال');
  }

  /// إعادة تهيئة الخدمة
  Future<void> restart() async {
    dispose();
    await initialize();
  }
}
