import 'dart:async';
import 'package:flutter/material.dart';
import '../models/Product.dart';
import '../services/FirebaseProductService.dart';

/// مزود المنتجات
class ProductProvider with ChangeNotifier {
  final FirebaseProductService _productService = FirebaseProductService();
  
  List<Product> _allProducts = [];
  List<Product> _filteredProducts = [];
  List<Product> _featuredProducts = [];
  List<Product> _latestProducts = [];
  List<Product> _mostViewedProducts = [];
  
  bool _isLoading = false;
  bool _isLoadingFeatured = false;
  bool _isLoadingLatest = false;
  bool _isLoadingMostViewed = false;
  String _errorMessage = '';
  String _currentFilter = '';
  String _currentStoreFilter = '';
  String _currentCategoryFilter = '';
  
  StreamSubscription<List<Product>>? _productsSubscription;

  // Getters
  List<Product> get allProducts => _allProducts;
  List<Product> get filteredProducts => _filteredProducts;
  List<Product> get featuredProducts => _featuredProducts;
  List<Product> get latestProducts => _latestProducts;
  List<Product> get mostViewedProducts => _mostViewedProducts;
  bool get isLoading => _isLoading;
  bool get isLoadingFeatured => _isLoadingFeatured;
  bool get isLoadingLatest => _isLoadingLatest;
  bool get isLoadingMostViewed => _isLoadingMostViewed;
  String get errorMessage => _errorMessage;
  String get currentFilter => _currentFilter;
  String get currentStoreFilter => _currentStoreFilter;
  String get currentCategoryFilter => _currentCategoryFilter;

  /// تهيئة المنتجات
  Future<void> initialize() async {
    await loadAllProducts();
    await loadFeaturedProducts();
    await loadLatestProducts();
    await loadMostViewedProducts();
  }

  /// تحميل جميع المنتجات
  Future<void> loadAllProducts() async {
    _setLoading(true);
    try {
      _allProducts = await _productService.getEnabledProducts();
      _filteredProducts = List.from(_allProducts);
      _errorMessage = '';
      print('تم تحميل ${_allProducts.length} منتج');
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المنتجات: $e';
      print(_errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل المنتجات المميزة
  Future<void> loadFeaturedProducts() async {
    _isLoadingFeatured = true;
    notifyListeners();
    
    try {
      _featuredProducts = await _productService.getFeaturedProducts();
      print('تم تحميل ${_featuredProducts.length} منتج مميز');
    } catch (e) {
      print('خطأ في تحميل المنتجات المميزة: $e');
    } finally {
      _isLoadingFeatured = false;
      notifyListeners();
    }
  }

  /// تحميل أحدث المنتجات
  Future<void> loadLatestProducts() async {
    _isLoadingLatest = true;
    notifyListeners();
    
    try {
      _latestProducts = await _productService.getLatestProducts(limit: 10);
      print('تم تحميل ${_latestProducts.length} من أحدث المنتجات');
    } catch (e) {
      print('خطأ في تحميل أحدث المنتجات: $e');
    } finally {
      _isLoadingLatest = false;
      notifyListeners();
    }
  }

  /// تحميل المنتجات الأكثر مشاهدة
  Future<void> loadMostViewedProducts() async {
    _isLoadingMostViewed = true;
    notifyListeners();
    
    try {
      _mostViewedProducts = await _productService.getMostViewedProducts(limit: 10);
      print('تم تحميل ${_mostViewedProducts.length} من المنتجات الأكثر مشاهدة');
    } catch (e) {
      print('خطأ في تحميل المنتجات الأكثر مشاهدة: $e');
    } finally {
      _isLoadingMostViewed = false;
      notifyListeners();
    }
  }

  /// تحميل منتجات متجر معين
  Future<void> loadProductsByStore(String storeId) async {
    _setLoading(true);
    _currentStoreFilter = storeId;
    
    try {
      _filteredProducts = await _productService.getProductsByStore(storeId);
      _errorMessage = '';
      print('تم تحميل ${_filteredProducts.length} منتج للمتجر: $storeId');
    } catch (e) {
      _errorMessage = 'خطأ في تحميل منتجات المتجر: $e';
      print(_errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل منتجات فئة معينة
  Future<void> loadProductsByCategory(String categoryId) async {
    _setLoading(true);
    _currentCategoryFilter = categoryId;
    
    try {
      _filteredProducts = await _productService.getProductsByCategory(categoryId);
      _errorMessage = '';
      print('تم تحميل ${_filteredProducts.length} منتج للفئة: $categoryId');
    } catch (e) {
      _errorMessage = 'خطأ في تحميل منتجات الفئة: $e';
      print(_errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// البحث في المنتجات
  Future<void> searchProducts(String searchTerm) async {
    _currentFilter = searchTerm;
    
    if (searchTerm.isEmpty) {
      _filteredProducts = List.from(_allProducts);
      notifyListeners();
      return;
    }

    _setLoading(true);
    try {
      // البحث في Firebase
      final firebaseResults = await _productService.searchProducts(searchTerm);
      
      // البحث المحلي كنسخة احتياطية
      final localResults = _allProducts.where((product) {
        return product.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
               product.description.toLowerCase().contains(searchTerm.toLowerCase());
      }).toList();

      // دمج النتائج
      final Set<String> seenIds = {};
      _filteredProducts = [];
      
      for (final product in [...firebaseResults, ...localResults]) {
        if (!seenIds.contains(product.id)) {
          seenIds.add(product.id);
          _filteredProducts.add(product);
        }
      }

      _errorMessage = '';
      print('تم العثور على ${_filteredProducts.length} منتج للبحث: $searchTerm');
    } catch (e) {
      _errorMessage = 'خطأ في البحث: $e';
      print(_errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// مسح الفلاتر
  void clearFilters() {
    _currentFilter = '';
    _currentStoreFilter = '';
    _currentCategoryFilter = '';
    _filteredProducts = List.from(_allProducts);
    notifyListeners();
  }

  /// الحصول على منتج حسب المعرف
  Future<Product?> getProductById(String productId) async {
    try {
      // البحث في المنتجات المحملة أولاً
      final localProduct = _allProducts.firstWhere(
        (product) => product.id == productId,
        orElse: () => _filteredProducts.firstWhere(
          (product) => product.id == productId,
          orElse: () => Product(
            id: '',
            name: '',
            description: '',
            price: 0,
            imageUrl: '',
            category: '',
          ),
        ),
      );

      if (localProduct.id.isNotEmpty) {
        return localProduct;
      }

      // إذا لم يوجد محلياً، جلبه من Firebase
      return await _productService.getProductById(productId);
    } catch (e) {
      print('خطأ في الحصول على المنتج: $e');
      return null;
    }
  }

  /// تحديث عدد المشاهدات
  Future<void> incrementProductViews(String productId) async {
    try {
      await _productService.incrementProductViews(productId);
      
      // تحديث المنتج محلياً
      _updateProductInLists(productId, (product) {
        return product.copyWith(orderCount: product.orderCount + 1);
      });
    } catch (e) {
      print('خطأ في تحديث عدد المشاهدات: $e');
    }
  }

  /// بدء الاستماع للتحديثات المباشرة
  void startListeningToProducts() {
    _productsSubscription?.cancel();
    _productsSubscription = _productService.getProductsStream().listen(
      (products) {
        _allProducts = products;
        if (_currentFilter.isEmpty && _currentStoreFilter.isEmpty && _currentCategoryFilter.isEmpty) {
          _filteredProducts = List.from(_allProducts);
        }
        notifyListeners();
        print('تم تحديث ${products.length} منتج من Firebase Stream');
      },
      onError: (error) {
        print('خطأ في stream المنتجات: $error');
      },
    );
  }

  /// إيقاف الاستماع للتحديثات
  void stopListeningToProducts() {
    _productsSubscription?.cancel();
    _productsSubscription = null;
  }

  /// تحديث منتج في جميع القوائم
  void _updateProductInLists(String productId, Product Function(Product) updater) {
    // تحديث في جميع المنتجات
    final allIndex = _allProducts.indexWhere((p) => p.id == productId);
    if (allIndex != -1) {
      _allProducts[allIndex] = updater(_allProducts[allIndex]);
    }

    // تحديث في المنتجات المفلترة
    final filteredIndex = _filteredProducts.indexWhere((p) => p.id == productId);
    if (filteredIndex != -1) {
      _filteredProducts[filteredIndex] = updater(_filteredProducts[filteredIndex]);
    }

    // تحديث في المنتجات المميزة
    final featuredIndex = _featuredProducts.indexWhere((p) => p.id == productId);
    if (featuredIndex != -1) {
      _featuredProducts[featuredIndex] = updater(_featuredProducts[featuredIndex]);
    }

    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// إعادة تحميل جميع البيانات
  Future<void> refresh() async {
    await initialize();
  }

  @override
  void dispose() {
    stopListeningToProducts();
    super.dispose();
  }
}
