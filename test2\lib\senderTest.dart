
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(ClientApp());
}

class ClientApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مرسل الطلب',
      home: OrderSenderScreen(),
    );
  }
}

class OrderSenderScreen extends StatelessWidget {
  final DatabaseReference ref = FirebaseDatabase.instance.ref("orders/rest_alshaybani");

  void sendOrder() {
    final newOrder = {
      "clientId": "user_123",
      "items": ["شاورما", "ماء"],
      "status": "pending",
      "createdAt": DateTime.now().toIso8601String(),
    };
    ref.push().set(newOrder);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("إرسال طلب")),
      body: Center(
        child: ElevatedButton(
          onPressed: sendOrder,
          child: Text("📤 أرسل طلب لمطعم الشيباني"),
        ),
      ),
    );
  }
}
