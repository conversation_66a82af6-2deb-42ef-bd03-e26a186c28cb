{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-39:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\26e1d6f843b8bd9c55fbe9cde85475ac\\transformed\\browser-1.4.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "11,12,13,14", "startColumns": "4,4,4,4", "startOffsets": "999,1097,1194,1303", "endColumns": "97,96,108,98", "endOffsets": "1092,1189,1298,1397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3927d342be7108a61372589dd032352d\\transformed\\core-1.13.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "4,5,6,7,8,9,10,15", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "281,377,479,578,677,781,883,1402", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "372,474,573,672,776,878,994,1498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\467f116ae41acb0fa99753049275ea75\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}}]}]}