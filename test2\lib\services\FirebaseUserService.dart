import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../models/UserProfile.dart';
import '../models/Address.dart';

/// خدمة Firebase لإدارة بيانات المستخدمين
class FirebaseUserService {
  static final FirebaseUserService _instance = FirebaseUserService._internal();
  factory FirebaseUserService() => _instance;
  FirebaseUserService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// مجموعة المستخدمين في Firestore
  static const String usersCollection = 'users';

  /// تسجيل مستخدم جديد وحفظ بياناته في Firebase
  Future<UserRegistrationResult> registerNewUser({
    required String firstName,
    required String lastName,
    required String phone,
    String? email,
    String? address,
    String? city,
    String? governorate,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = _validateUserData(
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        email: email,
      );

      if (!validationResult.isValid) {
        return UserRegistrationResult(
          success: false,
          message: validationResult.errorMessage!,
        );
      }

      // التحقق من عدم وجود المستخدم مسبقاً
      final existingUser = await _checkUserExists(phone);
      if (existingUser) {
        return UserRegistrationResult(
          success: false,
          message: 'رقم الهاتف مسجل مسبقاً',
        );
      }

      // إنشاء حساب مؤقت في Firebase Auth (إذا تم توفير إيميل)
      String? firebaseUserId;
      if (email != null && email.isNotEmpty) {
        try {
          // إنشاء كلمة مرور مؤقتة
          final tempPassword = _generateTempPassword(phone);
          
          final userCredential = await _auth.createUserWithEmailAndPassword(
            email: email,
            password: tempPassword,
          );
          
          firebaseUserId = userCredential.user?.uid;
          
          // تحديث اسم المستخدم
          await userCredential.user?.updateDisplayName('$firstName $lastName');
        } catch (authError) {
          print('خطأ في إنشاء حساب Firebase Auth: $authError');
          // يمكن المتابعة بدون Firebase Auth
        }
      }

      // إنشاء معرف فريد للمستخدم
      final userId = firebaseUserId ?? 'user_${DateTime.now().millisecondsSinceEpoch}';

      // إنشاء عنوان افتراضي إذا تم توفيره
      List<Address> addresses = [];
      String? defaultAddressId;
      
      if (address != null && address.isNotEmpty && city != null && city.isNotEmpty) {
        final defaultAddress = Address(
          id: 'addr_${DateTime.now().millisecondsSinceEpoch}',
          title: 'المنزل',
          street: address,
          district: governorate ?? 'حي رئيسي',
          city: city,
          buildingNumber: '1',
          fullAddress: '$address, $city${governorate != null ? ', $governorate' : ''}',
          isDefault: true,
        );
        
        addresses.add(defaultAddress);
        defaultAddressId = defaultAddress.id;
      }

      // إنشاء ملف المستخدم
      final userProfile = UserProfile(
        id: userId,
        firstName: firstName,
        lastName: lastName,
        email: email ?? '',
        phone: phone,
        addresses: addresses,
        defaultAddressId: defaultAddressId,
        preferences: {
          'language': 'ar',
          'notifications': true,
          'theme': 'light',
          'governorate': governorate,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ البيانات في Firestore
      await _saveUserToFirestore(userProfile);

      return UserRegistrationResult(
        success: true,
        message: 'تم تسجيل المستخدم بنجاح',
        userProfile: userProfile,
        firebaseUserId: firebaseUserId,
      );

    } catch (e) {
      print('خطأ في تسجيل المستخدم: $e');
      return UserRegistrationResult(
        success: false,
        message: 'حدث خطأ غير متوقع: ${e.toString()}',
      );
    }
  }

  /// حفظ بيانات المستخدم في Firestore
  Future<void> _saveUserToFirestore(UserProfile userProfile) async {
    try {
      final userData = userProfile.toMap();
      
      // إضافة معلومات إضافية للتتبع
      userData['registrationSource'] = 'mobile_app';
      userData['lastLoginAt'] = FieldValue.serverTimestamp();
      userData['isActive'] = true;
      userData['version'] = 1;

      await _firestore
          .collection(usersCollection)
          .doc(userProfile.id)
          .set(userData);

      print('تم حفظ بيانات المستخدم في Firestore: ${userProfile.id}');
    } catch (e) {
      print('خطأ في حفظ البيانات في Firestore: $e');
      throw Exception('فشل في حفظ البيانات');
    }
  }

  /// التحقق من وجود المستخدم مسبقاً
  Future<bool> _checkUserExists(String phone) async {
    try {
      final querySnapshot = await _firestore
          .collection(usersCollection)
          .where('phone', isEqualTo: phone)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      print('خطأ في التحقق من وجود المستخدم: $e');
      return false;
    }
  }

  /// التحقق من صحة بيانات المستخدم
  ValidationResult _validateUserData({
    required String firstName,
    required String lastName,
    required String phone,
    String? email,
  }) {
    // التحقق من الاسم الأول
    if (firstName.trim().isEmpty || firstName.trim().length < 2) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'يرجى إدخال الاسم الأول (حرفين على الأقل)',
      );
    }

    // التحقق من الاسم الأخير
    if (lastName.trim().isEmpty || lastName.trim().length < 2) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'يرجى إدخال الاسم الأخير (حرفين على الأقل)',
      );
    }

    // التحقق من رقم الهاتف
    if (!_isValidYemeniPhone(phone)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'رقم الهاتف يجب أن يكون 9 أرقام ويبدأ بالرقم 7',
      );
    }

    // التحقق من البريد الإلكتروني (إذا تم توفيره)
    if (email != null && email.isNotEmpty && !_isValidEmail(email)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'يرجى إدخال بريد إلكتروني صحيح',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// التحقق من صحة رقم الهاتف اليمني
  bool _isValidYemeniPhone(String phone) {
    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // التحقق من الطول والبداية
    return cleanPhone.length == 9 && cleanPhone.startsWith('7');
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  /// إنشاء كلمة مرور مؤقتة
  String _generateTempPassword(String phone) {
    final data = 'zad_$phone${DateTime.now().millisecondsSinceEpoch}';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 12);
  }

  /// جلب بيانات المستخدم من Firestore
  Future<UserProfile?> getUserById(String userId) async {
    try {
      final doc = await _firestore
          .collection(usersCollection)
          .doc(userId)
          .get();

      if (doc.exists && doc.data() != null) {
        return UserProfile.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  /// تحديث بيانات المستخدم
  Future<bool> updateUser(UserProfile userProfile) async {
    try {
      final userData = userProfile.toMap();
      userData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(usersCollection)
          .doc(userProfile.id)
          .update(userData);

      return true;
    } catch (e) {
      print('خطأ في تحديث بيانات المستخدم: $e');
      return false;
    }
  }

  /// حذف المستخدم
  Future<bool> deleteUser(String userId) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .delete();

      return true;
    } catch (e) {
      print('خطأ في حذف المستخدم: $e');
      return false;
    }
  }
}

/// نتيجة تسجيل المستخدم
class UserRegistrationResult {
  final bool success;
  final String message;
  final UserProfile? userProfile;
  final String? firebaseUserId;

  UserRegistrationResult({
    required this.success,
    required this.message,
    this.userProfile,
    this.firebaseUserId,
  });

  @override
  String toString() {
    return 'UserRegistrationResult(success: $success, message: $message)';
  }
}

/// نتيجة التحقق من صحة البيانات
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  ValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}
