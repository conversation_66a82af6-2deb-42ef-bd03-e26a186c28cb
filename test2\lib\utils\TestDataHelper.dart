import '../services/FirebaseStoresService.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// مساعد لإضافة بيانات تجريبية للاختبار
class TestDataHelper {
  static final FirebaseStoresService _storesService = FirebaseStoresService();

  /// إضافة محلات تجريبية إلى Firebase
  static Future<void> addSampleStores() async {
    // إضافة البيانات مباشرة إلى Firebase collection
    final firestore = FirebaseFirestore.instance;
    final storesCollection = firestore.collection('all_stores');

    final List<Map<String, dynamic>> storesData = [
      {
        'store_name': 'صيدلية الثور',
        'store_categ': 'صيدليات',
        'store_location': 'سعوان امام جولة النصر',
        'rating': 5.0,
        'deliveryTime': '20-30 دقيقة',
        'image': 'images/pharmacy.png',
        'isOpen': true,
        'phone': '+967771234567',
        'description': 'صيدلية متكاملة تقدم جميع الأدوية والمستلزمات الطبية',
        'tags': ['أدوية', 'صحة', 'طبية'],
        'deliveryFee': 2.0,
        'minimumOrder': 10.0,
        'reviewCount': 45,
        'isVerified': true,
        'isFeatured': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'store_name': 'سوبرماركت الأمانة',
        'store_categ': 'سوبر ماركت',
        'store_location': 'شارع الستين، صنعاء',
        'rating': 4.5,
        'deliveryTime': '25-35 دقيقة',
        'image': 'images/supermarket.png',
        'isOpen': true,
        'phone': '+967771234568',
        'description': 'سوبرماركت شامل لجميع احتياجاتك اليومية والمنزلية',
        'tags': ['بقالة', 'منزلية', 'يومية'],
        'deliveryFee': 3.0,
        'minimumOrder': 15.0,
        'reviewCount': 89,
        'isVerified': true,
        'isFeatured': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'store_name': 'مطعم البرجر الذهبي',
        'store_categ': 'مطاعم',
        'store_location': 'شارع الزبيري، صنعاء',
        'rating': 4.8,
        'deliveryTime': '30-40 دقيقة',
        'image': 'images/restaurant.png',
        'isOpen': true,
        'phone': '+967771234569',
        'description': 'مطعم متخصص في البرجر والوجبات السريعة اللذيذة',
        'tags': ['برجر', 'وجبات سريعة', 'لذيذ'],
        'deliveryFee': 4.0,
        'minimumOrder': 20.0,
        'reviewCount': 156,
        'isVerified': true,
        'isFeatured': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
      {
        'store_name': 'مخبز الفجر',
        'store_categ': 'مخابز',
        'store_location': 'شارع الثورة، صنعاء',
        'rating': 4.6,
        'deliveryTime': '15-25 دقيقة',
        'image': 'images/bakery.png',
        'isOpen': true,
        'phone': '+967771234570',
        'description': 'مخبز طازج يومياً مع أفضل أنواع الخبز والمعجنات',
        'tags': ['خبز', 'معجنات', 'طازج'],
        'deliveryFee': 1.0,
        'minimumOrder': 5.0,
        'reviewCount': 45,
        'isVerified': true,
        'isFeatured': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      },
    ];

    print('بدء إضافة ${storesData.length} محل تجريبي...');

    for (var storeData in storesData) {
      try {
        await storesCollection.add(storeData);
        print('✅ تم إضافة: ${storeData['store_name']}');

        // انتظار قصير بين كل إضافة
        await Future.delayed(Duration(milliseconds: 500));
      } catch (e) {
        print('❌ خطأ في إضافة ${storeData['store_name']}: $e');
      }
    }

    print('✅ انتهى من إضافة البيانات التجريبية');
  }

  /// إضافة محل جديد للاختبار (سيظهر إشعار)
  static Future<void> addNewStoreForTesting() async {
    final firestore = FirebaseFirestore.instance;
    final storesCollection = firestore.collection('all_stores');

    final newStoreData = {
      'store_name': 'مطعم البرجر الجديد',
      'store_categ': 'مطاعم',
      'store_location': 'شارع الخمسين، صنعاء',
      'rating': 4.2,
      'deliveryTime': '25-35 دقيقة',
      'image': 'images/new_store.png',
      'isOpen': true,
      'phone': '+967771234999',
      'description': 'مطعم برجر جديد مع أطعم الوجبات السريعة',
      'tags': ['برجر', 'وجبات سريعة', 'جديد'],
      'deliveryFee': 3.0,
      'minimumOrder': 15.0,
      'reviewCount': 0,
      'isVerified': false,
      'isFeatured': false,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };

    print('إضافة محل جديد للاختبار: ${newStoreData['store_name']}');

    try {
      await storesCollection.add(newStoreData);
      print('✅ تم إضافة المحل الجديد بنجاح - يجب أن يظهر إشعار');
    } catch (e) {
      print('❌ فشل في إضافة المحل الجديد: $e');
    }
  }

  /// حذف جميع البيانات التجريبية
  static Future<void> clearTestData() async {
    final testStoreIds = [
      'store_001',
      'store_002', 
      'store_003',
      'store_004',
      'store_005',
      'store_006',
    ];

    print('بدء حذف البيانات التجريبية...');
    
    for (var storeId in testStoreIds) {
      try {
        final success = await _storesService.deleteStore(storeId);
        if (success) {
          print('✅ تم حذف: $storeId');
        } else {
          print('❌ فشل في حذف: $storeId');
        }
      } catch (e) {
        print('❌ خطأ في حذف $storeId: $e');
      }
    }
    
    print('✅ انتهى من حذف البيانات التجريبية');
  }
}
