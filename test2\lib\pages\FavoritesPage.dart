import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/FavoritesProvider.dart';
import '../providers/StoresProvider.dart';
import '../providers/CartProvider.dart';
import '../widgets/CustomBottomNavBar.dart';
import '../widgets/OptimizedImage.dart';
import '../widgets/SearchWidget.dart';
import '../pages/StoreDetailsPage.dart';
import '../utils/DataManager.dart';
import '../models/Product.dart';

class FavoritesPage extends StatefulWidget {
  @override
  _FavoritesPageState createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // تحميل المفضلات عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFavorites();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadFavorites() async {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);
    final storesProvider = Provider.of<StoresProvider>(context, listen: false);

    await favoritesProvider.loadFavorites();

    // تحديث كاش المحلات المفضلة
    favoritesProvider.updateFavoriteStores(storesProvider.allStores);

    // تحديث كاش المنتجات المفضلة (من DataManager)
    final allItems = DataManager.allItems;
    final allProducts = allItems
        .map((item) => Product(
          id: item['id'].toString(),
          name: item['name'].toString(),
          description: item['description'].toString(),
          price: double.tryParse(item['price'].toString()) ?? 0.0,
          imageUrl: item['image']?.toString() ?? 'images/${item['id']}.png',
          category: item['category']?.toString() ?? 'عام',
          storeId: item['storeId']?.toString() ?? '',
          isAvailable: item['isAvailable'] ?? true,
        ))
        .toList();
    favoritesProvider.updateFavoriteProducts(allProducts);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'المفضلات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        actions: [
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              if (favoritesProvider.totalFavoritesCount > 0) {
                return IconButton(
                  icon: Icon(Icons.clear_all, color: Colors.white),
                  onPressed: () => _showClearAllDialog(context),
                  tooltip: 'مسح جميع المفضلات',
                );
              }
              return SizedBox.shrink();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: Icon(Icons.store),
              text: 'المحلات',
            ),
            Tab(
              icon: Icon(Icons.shopping_bag),
              text: 'المنتجات',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            color: Color(0xFF4C53A5),
            padding: EdgeInsets.all(12),
            child: Column(
              children: [
                // شريط البحث
                SearchWidget(
                  hintText: "ابحث في المفضلات...",
                  onSearchChanged: (query) {
                    setState(() {
                      _searchQuery = query;
                    });
                  },
                  showSuggestions: false,
                ),

                SizedBox(height: 10),

                // فلتر الفئات
                _buildCategoryFilter(),
              ],
            ),
          ),

          // المحتوى
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildStoresTab(),
                _buildProductsTab(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: FlatBottomNavBar(
            currentIndex: 2, // صفحة المفضلات
            onTap: (index) {
              NavigationHelper.navigateToPage(context, index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        // الحصول على الفئات المتاحة
        final categories = ['الكل'];

        if (_tabController.index == 0) {
          // فئات المحلات
          final storeCategories = favoritesProvider.favoriteStores
              .map((store) => store.category)
              .toSet()
              .toList();
          categories.addAll(storeCategories);
        } else {
          // فئات المنتجات
          final productCategories = favoritesProvider.favoriteProducts
              .map((product) => product.category)
              .toSet()
              .toList();
          categories.addAll(productCategories);
        }

        if (categories.length <= 1) return SizedBox.shrink();

        return Container(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = _selectedCategory == category;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(right: 8),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? Color(0xFF4C53A5) : Colors.white,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    category,
                    style: TextStyle(
                      color: isSelected ? Color(0xFF4C53A5) : Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildStoresTab() {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        if (favoritesProvider.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        if (favoritesProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  favoritesProvider.error!,
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadFavorites,
                  child: Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        // فلترة المحلات
        var stores = favoritesProvider.favoriteStores;

        if (_searchQuery.isNotEmpty) {
          stores = favoritesProvider.searchFavoriteStores(_searchQuery);
        }

        if (_selectedCategory != 'الكل') {
          stores = stores.where((store) => store.category == _selectedCategory).toList();
        }

        if (stores.isEmpty) {
          return _buildEmptyState(
            'لا توجد محلات مفضلة',
            'ابدأ بإضافة محلاتك المفضلة',
          );
        }

        return GridView.builder(
          padding: EdgeInsets.all(12),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.85,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: stores.length,
          itemBuilder: (context, index) {
            final store = stores[index];
            return _buildStoreCard(store);
          },
        );
      },
    );
  }

  Widget _buildProductsTab() {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        if (favoritesProvider.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        // فلترة المنتجات
        var products = favoritesProvider.favoriteProducts;

        if (_searchQuery.isNotEmpty) {
          products = favoritesProvider.searchFavoriteProducts(_searchQuery);
        }

        if (_selectedCategory != 'الكل') {
          products = products.where((product) => product.category == _selectedCategory).toList();
        }

        if (products.isEmpty) {
          return _buildEmptyState(
            'لا توجد منتجات مفضلة',
            'ابدأ بإضافة منتجاتك المفضلة',
          );
        }

        return GridView.builder(
          padding: EdgeInsets.all(12),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.85,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return _buildProductCard(product);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 20),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 10),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStoreCard(store) {
    return Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4C53A5).withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصف العلوي - نوع المتجر وحالة المحل والمفضلة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // نوع المتجر
                  Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4C53A5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      "متجر",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // حالة المحل (مفتوح/مغلق)
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: (store.isOpen ?? false)
                          ? Colors.green // أخضر للمفتوح
                          : Colors.red, // أحمر للمغلق
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      (store.isOpen ?? false) ? "مفتوح" : "مغلق",
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white, // نص أبيض
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              Consumer<FavoritesProvider>(
                builder: (context, favoritesProvider, child) {
                  final isFavorite = favoritesProvider.isStoreFavorite(store.id);
                  return GestureDetector(
                    onTap: () => _toggleStoreFavorite(store.id),
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.grey,
                    ),
                  );
                },
              )
            ],
          ),

          // صورة المتجر
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(10),
              child: OptimizedImage(
                imagePath: store.image ?? "images/1.png",
                width: 50,
                height: 50,
                fit: BoxFit.contain,
              ),
            ),
          ),

          // معلومات المتجر
          Container(
            padding: const EdgeInsets.only(bottom: 8),
            alignment: Alignment.centerRight,
            child: Text(
              store.name ?? "متجر غير معروف",
              style: const TextStyle(
                fontSize: 15,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          Container(
            alignment: Alignment.centerRight,
            child: Text(
              store.category ?? "عام",
              style: const TextStyle(fontSize: 11, color: Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // التقييم ووقت التوصيل
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      color: Colors.orange,
                      size: 12,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      "${store.rating ?? "4.5"}",
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
                Text(
                  store.deliveryTime ?? "30 دقيقة",
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildProductCard(product) {
    return Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصف العلوي - الخصم والمفضلة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 245, 170, 73),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  "-50%",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Consumer<FavoritesProvider>(
                builder: (context, favoritesProvider, child) {
                  final isFavorite = favoritesProvider.isProductFavorite(product.id);
                  return GestureDetector(
                    onTap: () => _toggleProductFavorite(product.id),
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.grey,
                    ),
                  );
                },
              )
            ],
          ),

          // صورة المنتج
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(10),
              child: OptimizedImage(
                imagePath: product.imageUrl ?? "images/${product.id}.png",
                width: 50,
                height: 50,
                fit: BoxFit.contain,
              ),
            ),
          ),

          // معلومات المنتج
          Container(
            padding: const EdgeInsets.only(bottom: 8),
            alignment: Alignment.centerRight,
            child: Text(
              product.name ?? "منتج غير معروف",
              style: const TextStyle(
                fontSize: 15,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          Container(
            alignment: Alignment.centerRight,
            child: Text(
              product.description ?? "وصف المنتج",
              style: const TextStyle(fontSize: 11, color: Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // السعر وزر الإضافة
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${product.price ?? "0"} ر.ي",
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Consumer<CartProvider>(
                  builder: (context, cartProvider, child) {
                    final isInCart = cartProvider.isProductInCart(product.id);
                    final quantity = cartProvider.getProductQuantity(product.id);

                    return GestureDetector(
                      onTap: () => _addToCart(product, cartProvider),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: isInCart
                              ? Colors.green
                              : const Color.fromARGB(255, 245, 170, 73),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isInCart
                                  ? Icons.check
                                  : Icons.shopping_cart_checkout,
                              color: Colors.white,
                              size: 12,
                            ),
                            if (isInCart && quantity > 0) ...[
                              const SizedBox(width: 4),
                              Text(
                                quantity.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void _toggleStoreFavorite(String storeId) {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);

    favoritesProvider.toggleStoreFavorite(storeId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isStoreFavorite(storeId);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isFavorite ? 'تم إضافة المحل للمفضلة' : 'تم إزالة المحل من المفضلة'),
            backgroundColor: isFavorite ? Colors.green : Colors.orange,
          ),
        );
      }
    });
  }

  void _toggleProductFavorite(String productId) {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);

    favoritesProvider.toggleProductFavorite(productId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isProductFavorite(productId);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isFavorite ? 'تم إضافة المنتج للمفضلة' : 'تم إزالة المنتج من المفضلة'),
            backgroundColor: isFavorite ? Colors.green : Colors.orange,
          ),
        );
      }
    });
  }

  void _addToCart(product, CartProvider cartProvider) {
    cartProvider.addToCart(product).then((success) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة ${product.name} إلى السلة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مسح جميع المفضلات'),
        content: Text('هل أنت متأكد من مسح جميع المفضلات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<FavoritesProvider>(context, listen: false).clearAllFavorites();
              Navigator.pop(context);
            },
            child: Text('مسح'),
          ),
        ],
      ),
    );
  }
}
