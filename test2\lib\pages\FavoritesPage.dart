import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/FavoritesProvider.dart';
import '../providers/StoresProvider.dart';
import '../widgets/CustomBottomNavBar.dart';
import '../widgets/OptimizedImage.dart';
import '../widgets/SearchWidget.dart';
import '../pages/StoreDetailsPage.dart';
import '../utils/DataManager.dart';
import '../models/Product.dart';

class FavoritesPage extends StatefulWidget {
  @override
  _FavoritesPageState createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // تحميل المفضلات عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFavorites();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadFavorites() async {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);
    final storesProvider = Provider.of<StoresProvider>(context, listen: false);

    await favoritesProvider.loadFavorites();

    // تحديث كاش المحلات المفضلة
    favoritesProvider.updateFavoriteStores(storesProvider.allStores);

    // تحديث كاش المنتجات المفضلة (من DataManager)
    final allItems = DataManager.allItems;
    final allProducts = allItems
        .map((item) => Product(
          id: item['id'].toString(),
          name: item['name'].toString(),
          description: item['description'].toString(),
          price: double.tryParse(item['price'].toString()) ?? 0.0,
          imageUrl: item['image']?.toString() ?? 'images/${item['id']}.png',
          category: item['category']?.toString() ?? 'عام',
          storeId: item['storeId']?.toString() ?? '',
          isAvailable: item['isAvailable'] ?? true,
        ))
        .toList();
    favoritesProvider.updateFavoriteProducts(allProducts);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'المفضلات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        actions: [
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              if (favoritesProvider.totalFavoritesCount > 0) {
                return IconButton(
                  icon: Icon(Icons.clear_all, color: Colors.white),
                  onPressed: () => _showClearAllDialog(context),
                  tooltip: 'مسح جميع المفضلات',
                );
              }
              return SizedBox.shrink();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: Icon(Icons.store),
              text: 'المحلات',
            ),
            Tab(
              icon: Icon(Icons.shopping_bag),
              text: 'المنتجات',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            color: Color(0xFF4C53A5),
            padding: EdgeInsets.all(12),
            child: Column(
              children: [
                // شريط البحث
                SearchWidget(
                  hintText: "ابحث في المفضلات...",
                  onSearchChanged: (query) {
                    setState(() {
                      _searchQuery = query;
                    });
                  },
                  showSuggestions: false,
                ),

                SizedBox(height: 10),

                // فلتر الفئات
                _buildCategoryFilter(),
              ],
            ),
          ),

          // المحتوى
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildStoresTab(),
                _buildProductsTab(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: FlatBottomNavBar(
            currentIndex: 2, // صفحة المفضلات
            onTap: (index) {
              NavigationHelper.navigateToPage(context, index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return SizedBox.shrink(); // مؤقت
  }

  Widget _buildStoresTab() {
    return Center(child: Text('المحلات المفضلة'));
  }

  Widget _buildProductsTab() {
    return Center(child: Text('المنتجات المفضلة'));
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مسح جميع المفضلات'),
        content: Text('هل أنت متأكد من مسح جميع المفضلات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<FavoritesProvider>(context, listen: false).clearAllFavorites();
              Navigator.pop(context);
            },
            child: Text('مسح'),
          ),
        ],
      ),
    );
  }
}
