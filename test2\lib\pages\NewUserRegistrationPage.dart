import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../services/FirebaseUserService.dart';
import '../providers/CustomerDataProvider.dart';
import '../providers/AuthenticationProvider.dart';
import '../utils/AppColors.dart';

/// صفحة تسجيل المستخدم الجديد
class NewUserRegistrationPage extends StatefulWidget {
  @override
  _NewUserRegistrationPageState createState() => _NewUserRegistrationPageState();
}

class _NewUserRegistrationPageState extends State<NewUserRegistrationPage> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();

  final FirebaseUserService _firebaseUserService = FirebaseUserService();
  
  bool _isLoading = false;
  String? _selectedGovernorate;

  // قائمة المحافظات اليمنية
  final List<String> _yemeniGovernorates = [
    'صنعاء',
    'عدن',
    'تعز',
    'الحديدة',
    'إب',
    'ذمار',
    'صعدة',
    'حجة',
    'أبين',
    'لحج',
    'البيضاء',
    'الجوف',
    'مأرب',
    'شبوة',
    'حضرموت',
    'المهرة',
    'سقطرى',
    'عمران',
    'الضالع',
    'ريمة',
    'المحويت',
    'الأمانة (صنعاء)',
  ];

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 40.h),
              
              // العنوان والترحيب
              _buildHeader(),
              
              SizedBox(height: 40.h),
              
              // نموذج التسجيل
              _buildRegistrationForm(),
              
              SizedBox(height: 30.h),
              
              // زر التسجيل
              _buildRegisterButton(),
              
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // شعار التطبيق
        Center(
          child: Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(
              Icons.person_add,
              color: Colors.white,
              size: 40.sp,
            ),
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // العنوان
        Center(
          child: Text(
            'مرحباً بك في زاد',
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
        ),
        
        SizedBox(height: 10.h),
        
        // الوصف
        Center(
          child: Text(
            'يرجى إدخال بياناتك الشخصية لإكمال التسجيل',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// بناء نموذج التسجيل
  Widget _buildRegistrationForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // الاسم الأول
          _buildTextField(
            controller: _firstNameController,
            label: 'الاسم الأول',
            icon: Icons.person,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال الاسم الأول';
              }
              if (value.trim().length < 2) {
                return 'الاسم يجب أن يكون حرفين على الأقل';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // الاسم الأخير
          _buildTextField(
            controller: _lastNameController,
            label: 'الاسم الأخير',
            icon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال الاسم الأخير';
              }
              if (value.trim().length < 2) {
                return 'الاسم يجب أن يكون حرفين على الأقل';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // رقم الهاتف
          _buildTextField(
            controller: _phoneController,
            label: 'رقم الهاتف',
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
            prefix: '+967 ',
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال رقم الهاتف';
              }
              final cleanPhone = value.replaceAll(RegExp(r'[^\d]'), '');
              if (cleanPhone.length != 9 || !cleanPhone.startsWith('7')) {
                return 'رقم الهاتف يجب أن يكون 9 أرقام ويبدأ بالرقم 7';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // البريد الإلكتروني (اختياري)
          _buildTextField(
            controller: _emailController,
            label: 'البريد الإلكتروني (اختياري)',
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // المحافظة
          _buildGovernorateDropdown(),
          
          SizedBox(height: 16.h),
          
          // المدينة
          _buildTextField(
            controller: _cityController,
            label: 'المدينة',
            icon: Icons.location_city,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال المدينة';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // العنوان
          _buildTextField(
            controller: _addressController,
            label: 'العنوان التفصيلي',
            icon: Icons.location_on,
            maxLines: 2,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال العنوان';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// بناء حقل النص
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? prefix,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primaryColor),
        prefixText: prefix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.primaryColor.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
    );
  }

  /// بناء قائمة المحافظات
  Widget _buildGovernorateDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGovernorate,
      decoration: InputDecoration(
        labelText: 'المحافظة',
        prefixIcon: Icon(Icons.map, color: AppColors.primaryColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.primaryColor.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: _yemeniGovernorates.map((governorate) {
        return DropdownMenuItem<String>(
          value: governorate,
          child: Text(governorate),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedGovernorate = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار المحافظة';
        }
        return null;
      },
    );
  }

  /// بناء زر التسجيل
  Widget _buildRegisterButton() {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleRegistration,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? CircularProgressIndicator(color: Colors.white)
            : Text(
                'إكمال التسجيل',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  /// معالجة التسجيل
  Future<void> _handleRegistration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _firebaseUserService.registerNewUser(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        phone: _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        address: _addressController.text.trim(),
        city: _cityController.text.trim(),
        governorate: _selectedGovernorate,
      );

      if (result.success && result.userProfile != null) {
        // حفظ البيانات في النظام المحلي
        final customerProvider = Provider.of<CustomerDataProvider>(context, listen: false);
        await customerProvider.saveCustomerData(result.userProfile!);

        // تحديث حالة المصادقة
        final authProvider = Provider.of<AuthenticationProvider>(context, listen: false);
        await authProvider.initializeAuth();

        // عرض رسالة نجاح
        _showSuccessDialog();
      } else {
        _showErrorDialog(result.message);
      }
    } catch (e) {
      _showErrorDialog('حدث خطأ غير متوقع: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض رسالة النجاح
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 10.w),
            Text('تم التسجيل بنجاح'),
          ],
        ),
        content: Text('مرحباً بك في تطبيق زاد! سيتم توجيهك للصفحة الرئيسية.'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushReplacementNamed('/');
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primaryColor),
            child: Text('متابعة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة الخطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 10.w),
            Text('خطأ في التسجيل'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
