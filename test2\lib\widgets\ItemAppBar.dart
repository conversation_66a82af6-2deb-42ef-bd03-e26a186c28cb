import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/FavoritesProvider.dart';
import '../widgets/CustomSnackBars.dart';

class ItemAppBar extends StatelessWidget implements PreferredSizeWidget{
  final String? productId;

  const ItemAppBar({Key? key, this.productId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color.fromARGB(255, 219, 55, 80),
      padding: EdgeInsets.only(top: 25,left: 25,right: 25,bottom: 5),
      margin: EdgeInsets.only(top: 15),
      child: Row(
        children: [
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              if (productId == null) {
                return Icon(
                  Icons.favorite_border,
                  size: 30,
                  color: Colors.grey,
                );
              }

              final isFavorite = favoritesProvider.isProductFavorite(productId!);
              return GestureDetector(
                onTap: () => _toggleProductFavorite(context, productId!, favoritesProvider),
                child: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  size: 30,
                  color: isFavorite ? Colors.red : Color.fromARGB(255, 245, 170, 73),
                ),
              );
            },
          ),

          Spacer(),
          Padding(
            padding:EdgeInsets.only(right: 20),
            child: Text(
              "اسم المنتج",
              style: TextStyle(
                fontSize: 23,
                fontWeight: FontWeight.bold,
                color: Colors.white
              ),
            ), 
          ),
          
          InkWell(
            onTap: (){
              Navigator.pop(context);
            },
            child: Icon(
              Icons.arrow_forward,
              size: 30,
              color: Colors.white,
            ),
          ),
          
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(50);

  /// تبديل حالة المفضلة للمنتج
  void _toggleProductFavorite(BuildContext context, String productId, FavoritesProvider favoritesProvider) {
    favoritesProvider.toggleProductFavorite(productId).then((success) {
      if (success) {
        final isFavorite = favoritesProvider.isProductFavorite(productId);
        CustomSnackBars.showSuccess(
          context,
          message: isFavorite ? 'تم إضافة المنتج للمفضلة' : 'تم إزالة المنتج من المفضلة',
          subtitle: isFavorite ? 'يمكنك العثور عليه في صفحة المفضلات' : '',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في تحديث المفضلة',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }
}