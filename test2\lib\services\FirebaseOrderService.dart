import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/Order.dart' as AppOrder;
import '../models/CartItem.dart';
import '../models/Address.dart';
import '../models/UserProfile.dart';
import 'CustomerDataService.dart';

/// خدمة Firebase للطلبات
class FirebaseOrderService {
  static final FirebaseOrderService _instance = FirebaseOrderService._internal();
  factory FirebaseOrderService() => _instance;
  FirebaseOrderService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// مجموعة الطلبات في Firestore
  static const String ordersCollection = 'orders';

  /// إرسال طلب جديد إلى Firebase
  Future<bool> createOrder(AppOrder.Order order) async {
    try {
      print('بدء إرسال الطلب إلى Firebase: ${order.id}');
      print('نوع PaymentMethod: ${order.paymentMethod.runtimeType}');
      print('قيمة PaymentMethod: ${order.paymentMethod}');

      try {
        // تحويل الطلب إلى تنسيق Firebase
        print('بدء تحويل الطلب إلى تنسيق Firebase...');
        final orderData = _orderToFirestore(order);
        print('تم تحويل الطلب بنجاح');

        print('تحقق من بيانات الطلب قبل الإرسال:');
        print('PaymentMethod في البيانات: ${orderData['paymentMethod']}');
        print('نوع PaymentMethod في البيانات: ${orderData['paymentMethod'].runtimeType}');

        // إرسال الطلب إلى Firebase
        print('بدء إرسال البيانات إلى Firebase...');
        await _firestore
            .collection(ordersCollection)
            .doc(order.id)
            .set(orderData);

        print('تم إرسال الطلب بنجاح إلى Firebase: ${order.id}');
        return true;
      } catch (conversionError) {
        print('خطأ في تحويل البيانات: $conversionError');
        rethrow;
      }
    } catch (e) {
      print('خطأ في إرسال الطلب إلى Firebase: $e');
      return false;
    }
  }

  /// إنشاء طلب جديد باستخدام بيانات العميل وإرساله إلى Firebase
  Future<AppOrder.Order?> createOrderFromCustomerData({
    required Address deliveryAddress,
    required List<CartItem> items,
    required String paymentMethod,
    required String deliveryTime,
    DateTime? scheduledTime,
    String? notes,
  }) async {
    try {
      // الحصول على بيانات العميل
      final customerService = CustomerDataService();
      await customerService.ensureDataLoaded();
      final userProfile = customerService.customerProfile;
      if (userProfile == null) {
        throw Exception('لم يتم العثور على بيانات العميل');
      }

      // إنشاء معرف فريد للطلب
      final orderId = 'ORD_${DateTime.now().millisecondsSinceEpoch}';
      
      // حساب المبالغ
      final subtotal = items.fold<double>(0, (total, item) => total + (item.price * item.quantity));
      final deliveryFee = subtotal >= 50 ? 0.0 : 10.0; // توصيل مجاني للطلبات أكثر من 50 ريال
      final tax = subtotal * 0.15; // ضريبة القيمة المضافة 15%
      const discount = 0.0; // يمكن إضافة منطق الخصومات لاحقاً
      final total = subtotal + deliveryFee + tax - discount;

      // إنشاء الطلب
      final order = AppOrder.Order(
        id: orderId,
        userId: userProfile.id,
        userName: userProfile.fullName,
        userPhone: userProfile.phone,
        userEmail: userProfile.email,
        deliveryAddress: deliveryAddress,
        items: items,
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        tax: tax,
        discount: discount,
        total: total,
        status: AppOrder.OrderStatus.pending,
        paymentMethod: _parsePaymentMethod(paymentMethod),
        deliveryTime: _parseDeliveryTime(deliveryTime),
        scheduledTime: scheduledTime,
        notes: notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        statusHistory: [
          AppOrder.OrderStatusUpdate(
            status: AppOrder.OrderStatus.pending,
            timestamp: DateTime.now(),
            message: 'تم إنشاء الطلب',
            updatedBy: userProfile.id,
            updatedByRole: 'customer',
          ),
        ],
        // إضافة معلومات المتجر من أول منتج
        storeId: items.isNotEmpty ? items.first.storeId : null,
        storeName: items.isNotEmpty ? items.first.storeName : null,
        estimatedDeliveryMinutes: 30.0, // تقدير افتراضي
      );

      // إرسال الطلب إلى Firebase
      final success = await createOrder(order);
      
      if (success) {
        print('تم إنشاء وإرسال الطلب بنجاح: ${order.id}');
        return order;
      } else {
        throw Exception('فشل في إرسال الطلب إلى Firebase');
      }
    } catch (e) {
      print('خطأ في إنشاء الطلب: $e');
      return null;
    }
  }

  /// تحويل الطلب إلى تنسيق Firebase
  Map<String, dynamic> _orderToFirestore(AppOrder.Order order) {
    print('تحويل PaymentMethod: ${order.paymentMethod} إلى ${order.paymentMethod.toString().split('.').last}');
    final orderData = {
      'id': order.id,
      'userId': order.userId,
      'userName': order.userName,
      'userPhone': order.userPhone,
      'userEmail': order.userEmail,
      'deliveryAddress': {
        'street': order.deliveryAddress.street,
        'city': order.deliveryAddress.city,
        'district': order.deliveryAddress.district,
        'buildingNumber': order.deliveryAddress.buildingNumber,
        'apartmentNumber': order.deliveryAddress.apartmentNumber,
        'fullAddress': order.deliveryAddress.fullAddress,
        'latitude': order.deliveryAddress.latitude,
        'longitude': order.deliveryAddress.longitude,
        'landmark': order.deliveryAddress.landmark,
        'additionalInfo': order.deliveryAddress.additionalInfo,
      },
      'items': order.items.map((item) => {
        'id': item.id,
        'name': item.name,
        'price': item.price,
        'quantity': item.quantity,
        'imageUrl': item.imageUrl,
        'storeId': item.storeId,
        'storeName': item.storeName,
        'category': item.category,
        'description': item.description,
      }).toList(),
      'subtotal': order.subtotal,
      'deliveryFee': order.deliveryFee,
      'tax': order.tax,
      'discount': order.discount,
      'total': order.total,
      'status': order.status.toString().split('.').last,
      'paymentMethod': order.paymentMethod.toString().split('.').last,
      'deliveryTime': order.deliveryTime.toString().split('.').last,
      'scheduledTime': order.scheduledTime?.toIso8601String(),
      'notes': order.notes,
      'createdAt': Timestamp.fromDate(order.createdAt),
      'updatedAt': Timestamp.fromDate(order.updatedAt),
      'statusHistory': order.statusHistory.map((update) => {
        'status': update.status.toString().split('.').last,
        'timestamp': Timestamp.fromDate(update.timestamp),
        'message': update.message,
        'updatedBy': update.updatedBy,
        'updatedByRole': update.updatedByRole,
      }).toList(),
      'storeId': order.storeId,
      'storeName': order.storeName,
      'deliveryPersonId': order.deliveryPersonId,
      'deliveryPersonName': order.deliveryPersonName,
      'deliveryPersonPhone': order.deliveryPersonPhone,
      'estimatedDeliveryMinutes': order.estimatedDeliveryMinutes,
      // إضافة معلومات إضافية للإدارة
      'orderSource': 'mobile_app',
      'appVersion': '1.0.0',
      'platform': 'flutter',
    };

    print('بيانات الطلب المحولة: ${orderData['paymentMethod']}');
    return orderData;
  }

  /// تحليل طريقة الدفع
  AppOrder.PaymentMethod _parsePaymentMethod(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'نقدي':
      case 'cash':
        return AppOrder.PaymentMethod.cash;
      case 'بطاقة ائتمان':
      case 'card':
        return AppOrder.PaymentMethod.card;
      case 'آبل باي':
      case 'apple pay':
      case 'applepay':
        return AppOrder.PaymentMethod.applePay;
      case 'stc pay':
      case 'stcpay':
        return AppOrder.PaymentMethod.stcPay;
      case 'مدى':
      case 'mada':
        return AppOrder.PaymentMethod.mada;
      default:
        return AppOrder.PaymentMethod.cash;
    }
  }

  /// تحليل وقت التوصيل
  AppOrder.DeliveryTime _parseDeliveryTime(String deliveryTime) {
    switch (deliveryTime.toLowerCase()) {
      case 'في أقرب وقت':
      case 'asap':
        return AppOrder.DeliveryTime.asap;
      case 'وقت محدد':
      case 'scheduled':
        return AppOrder.DeliveryTime.scheduled;
      default:
        return AppOrder.DeliveryTime.asap;
    }
  }

  /// جلب طلبات المستخدم من Firebase
  Future<List<AppOrder.Order>> getUserOrders(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(ordersCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => _orderFromFirestore(doc))
          .toList();
    } catch (e) {
      print('خطأ في جلب طلبات المستخدم: $e');
      return [];
    }
  }

  /// تحويل مستند Firebase إلى طلب
  AppOrder.Order _orderFromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AppOrder.Order(
      id: data['id'] ?? '',
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userPhone: data['userPhone'] ?? '',
      userEmail: data['userEmail'] ?? '',
      deliveryAddress: Address.fromMap(data['deliveryAddress'] ?? {}),
      items: (data['items'] as List?)
              ?.map((item) => CartItem.fromMap(item))
              .toList() ??
          [],
      subtotal: (data['subtotal'] ?? 0).toDouble(),
      deliveryFee: (data['deliveryFee'] ?? 0).toDouble(),
      tax: (data['tax'] ?? 0).toDouble(),
      discount: (data['discount'] ?? 0).toDouble(),
      total: (data['total'] ?? 0).toDouble(),
      status: AppOrder.OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == data['status'],
        orElse: () => AppOrder.OrderStatus.pending,
      ),
      paymentMethod: AppOrder.PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == data['paymentMethod'],
        orElse: () => AppOrder.PaymentMethod.cash,
      ),
      deliveryTime: AppOrder.DeliveryTime.values.firstWhere(
        (e) => e.toString().split('.').last == data['deliveryTime'],
        orElse: () => AppOrder.DeliveryTime.asap,
      ),
      scheduledTime: data['scheduledTime'] != null
          ? DateTime.tryParse(data['scheduledTime'])
          : null,
      notes: data['notes'],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      statusHistory: (data['statusHistory'] as List?)
              ?.map((update) => AppOrder.OrderStatusUpdate.fromMap(update))
              .toList() ??
          [],
      storeId: data['storeId'],
      storeName: data['storeName'],
      deliveryPersonId: data['deliveryPersonId'],
      deliveryPersonName: data['deliveryPersonName'],
      deliveryPersonPhone: data['deliveryPersonPhone'],
      estimatedDeliveryMinutes: data['estimatedDeliveryMinutes']?.toDouble(),
    );
  }

  /// تحديث حالة الطلب
  Future<bool> updateOrderStatus(String orderId, AppOrder.OrderStatus newStatus, {String? message}) async {
    try {
      await _firestore
          .collection(ordersCollection)
          .doc(orderId)
          .update({
        'status': newStatus.toString().split('.').last,
        'updatedAt': Timestamp.now(),
        'statusHistory': FieldValue.arrayUnion([
          {
            'status': newStatus.toString().split('.').last,
            'timestamp': Timestamp.now(),
            'message': message ?? 'تم تحديث حالة الطلب',
            'updatedBy': 'system',
            'updatedByRole': 'system',
          }
        ]),
      });
      
      print('تم تحديث حالة الطلب: $orderId إلى $newStatus');
      return true;
    } catch (e) {
      print('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }
}
