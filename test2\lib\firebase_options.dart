// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDBkJpNyFraBZtEf0BL-eVxxboOhgCQr5Y',
    appId: '1:920830838081:web:0ea137f7484d2a57e3cdc0',
    messagingSenderId: '920830838081',
    projectId: 'zad-k-2e466',
    authDomain: 'zad-k-2e466.firebaseapp.com',
    databaseURL: 'https://zad-k-2e466-default-rtdb.firebaseio.com',
    storageBucket: 'zad-k-2e466.firebasestorage.app',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDBkJpNyFraBZtEf0BL-eVxxboOhgCQr5Y',
    appId: '1:920830838081:android:0ea137f7484d2a57e3cdc0',
    messagingSenderId: '920830838081',
    projectId: 'zad-k-2e466',
    databaseURL: 'https://zad-k-2e466-default-rtdb.firebaseio.com',
    storageBucket: 'zad-k-2e466.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDBkJpNyFraBZtEf0BL-eVxxboOhgCQr5Y',
    appId: '1:920830838081:ios:0ea137f7484d2a57e3cdc0',
    messagingSenderId: '920830838081',
    projectId: 'zad-k-2e466',
    databaseURL: 'https://zad-k-2e466-default-rtdb.firebaseio.com',
    storageBucket: 'zad-k-2e466.firebasestorage.app',
    iosBundleId: 'com.example.test2',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDBkJpNyFraBZtEf0BL-eVxxboOhgCQr5Y',
    appId: '1:920830838081:ios:0ea137f7484d2a57e3cdc0',
    messagingSenderId: '920830838081',
    projectId: 'zad-k-2e466',
    databaseURL: 'https://zad-k-2e466-default-rtdb.firebaseio.com',
    storageBucket: 'zad-k-2e466.firebasestorage.app',
    iosBundleId: 'com.example.test2',
  );
}
