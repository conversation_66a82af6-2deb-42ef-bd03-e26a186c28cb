com.example.test2.app-jetified-play-services-maps-18.2.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\res
com.example.test2.app-jetified-firebase-messaging-24.1.1-1 C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\res
com.example.test2.app-room-runtime-2.5.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\res
com.example.test2.app-jetified-datastore-1.0.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\19e47acafca7bbbde4842fc23193991e\transformed\jetified-datastore-1.0.0\res
com.example.test2.app-media-1.1.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\1bc5e3771ddad17cb7465c50c7aa900d\transformed\media-1.1.0\res
com.example.test2.app-browser-1.4.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\26e1d6f843b8bd9c55fbe9cde85475ac\transformed\browser-1.4.0\res
com.example.test2.app-lifecycle-livedata-core-2.6.2-6 C:\Users\<USER>\.gradle\caches\transforms-3\3730b3053d44029cc86c70e0dd7f0f80\transformed\lifecycle-livedata-core-2.6.2\res
com.example.test2.app-core-1.13.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\res
com.example.test2.app-jetified-play-services-basement-18.5.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\res
com.example.test2.app-jetified-window-1.0.0-beta04-9 C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\res
com.example.test2.app-jetified-core-common-2.0.3-10 C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\res
com.example.test2.app-jetified-credentials-1.2.0-rc01-11 C:\Users\<USER>\.gradle\caches\transforms-3\467f116ae41acb0fa99753049275ea75\transformed\jetified-credentials-1.2.0-rc01\res
com.example.test2.app-jetified-credentials-play-services-auth-1.2.0-rc01-12 C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.test2.app-jetified-firebase-common-21.0.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\res
com.example.test2.app-jetified-startup-runtime-1.1.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\50b502852e61bf607df9cdf3b57dfad2\transformed\jetified-startup-runtime-1.1.1\res
com.example.test2.app-core-runtime-2.2.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\538862e1ffaf780c18673d68e0dcd114\transformed\core-runtime-2.2.0\res
com.example.test2.app-jetified-ads-adservices-java-1.1.0-beta11-16 C:\Users\<USER>\.gradle\caches\transforms-3\57269b20e8affadaf797641cc7b3ca84\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.test2.app-jetified-datastore-preferences-1.0.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\5937ce1cb8495edf85107ab73d297087\transformed\jetified-datastore-preferences-1.0.0\res
com.example.test2.app-jetified-ads-adservices-1.1.0-beta11-18 C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.test2.app-jetified-window-java-1.0.0-beta04-19 C:\Users\<USER>\.gradle\caches\transforms-3\5fc770aa31a57680d21a5291bdece3c3\transformed\jetified-window-java-1.0.0-beta04\res
com.example.test2.app-lifecycle-runtime-2.6.2-20 C:\Users\<USER>\.gradle\caches\transforms-3\6de874d0ddfc66f7664f75b89ce9d9df\transformed\lifecycle-runtime-2.6.2\res
com.example.test2.app-jetified-lifecycle-service-2.6.2-21 C:\Users\<USER>\.gradle\caches\transforms-3\6f2d32593d2b21abce6ffe3efcda53e2\transformed\jetified-lifecycle-service-2.6.2\res
com.example.test2.app-lifecycle-viewmodel-2.6.2-22 C:\Users\<USER>\.gradle\caches\transforms-3\745573af90b7d28a08508c3615472ffb\transformed\lifecycle-viewmodel-2.6.2\res
com.example.test2.app-localbroadcastmanager-1.1.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\8414e66516de9bcf3a216884d75985fb\transformed\localbroadcastmanager-1.1.0\res
com.example.test2.app-lifecycle-livedata-2.6.2-24 C:\Users\<USER>\.gradle\caches\transforms-3\90877ba2dcbf931664f73c486ead0788\transformed\lifecycle-livedata-2.6.2\res
com.example.test2.app-sqlite-framework-2.3.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\9c194f53f4d1177633baf536c608a5c6\transformed\sqlite-framework-2.3.0\res
com.example.test2.app-sqlite-2.3.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\a9f6a94219becb7f51a931a8cb4a91b5\transformed\sqlite-2.3.0\res
com.example.test2.app-work-runtime-2.8.1-27 C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\res
com.example.test2.app-jetified-core-ktx-1.13.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\c100727f28ee2e9f4bc8f3a3d0cc866e\transformed\jetified-core-ktx-1.13.0\res
com.example.test2.app-jetified-annotation-experimental-1.4.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\db23986921d173f496db72240d019e2d\transformed\jetified-annotation-experimental-1.4.0\res
com.example.test2.app-jetified-android-maps-utils-3.6.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\db36947d2d0e3bbea45e70638ee3951f\transformed\jetified-android-maps-utils-3.6.0\res
com.example.test2.app-jetified-profileinstaller-1.3.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\res
com.example.test2.app-jetified-play-services-base-18.5.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\res
com.example.test2.app-jetified-play-services-auth-20.7.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\res
com.example.test2.app-debug-34 D:\augments_projects\appss\test2\android\app\src\debug\res
com.example.test2.app-main-35 D:\augments_projects\appss\test2\android\app\src\main\res
com.example.test2.app-pngs-36 D:\augments_projects\appss\test2\build\app\generated\res\pngs\debug
com.example.test2.app-res-37 D:\augments_projects\appss\test2\build\app\generated\res\processDebugGoogleServices
com.example.test2.app-resValues-38 D:\augments_projects\appss\test2\build\app\generated\res\resValues\debug
com.example.test2.app-mergeDebugResources-39 D:\augments_projects\appss\test2\build\app\intermediates\incremental\debug\mergeDebugResources\merged.dir
com.example.test2.app-mergeDebugResources-40 D:\augments_projects\appss\test2\build\app\intermediates\incremental\debug\mergeDebugResources\stripped.dir
com.example.test2.app-merged_res-41 D:\augments_projects\appss\test2\build\app\intermediates\merged_res\debug
com.example.test2.app-packaged_res-42 D:\augments_projects\appss\test2\build\audioplayers_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-43 D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-44 D:\augments_projects\appss\test2\build\connectivity_plus\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-45 D:\augments_projects\appss\test2\build\firebase_auth\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-46 D:\augments_projects\appss\test2\build\firebase_core\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-47 D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-48 D:\augments_projects\appss\test2\build\flutter_local_notifications\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-49 D:\augments_projects\appss\test2\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-50 D:\augments_projects\appss\test2\build\geocoding_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-51 D:\augments_projects\appss\test2\build\geolocator_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-52 D:\augments_projects\appss\test2\build\google_maps_flutter_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-53 D:\augments_projects\appss\test2\build\path_provider_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-54 D:\augments_projects\appss\test2\build\permission_handler_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-55 D:\augments_projects\appss\test2\build\shared_preferences_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-56 D:\augments_projects\appss\test2\build\workmanager\intermediates\packaged_res\debug
