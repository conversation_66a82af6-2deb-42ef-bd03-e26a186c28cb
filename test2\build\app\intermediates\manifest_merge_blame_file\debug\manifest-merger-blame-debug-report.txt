1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.test2"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:6:5-67
15-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:4:5-79
17-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:4:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- إذن للوصول لحالة الشبكة -->
18-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:5:5-81
18-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:5:22-78
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:9:5-79
19-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:9:22-76
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:52:5-57:15
28        <intent>
28-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:53:9-56:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:54:13-72
29-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:54:21-70
30
31            <data android:mimeType="text/plain" />
31-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
31-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:19-48
32        </intent>
33        <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
37-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
37-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-65
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
38-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
38-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
39    <uses-permission android:name="android.permission.VIBRATE" />
39-->[:flutter_local_notifications] D:\augments_projects\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
39-->[:flutter_local_notifications] D:\augments_projects\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
40    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
42
43    <uses-feature
43-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
44        android:glEsVersion="0x00020000"
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
45        android:required="true" />
45-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
46
47    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
47-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
47-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
48    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
48-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
49    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
49-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
49-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
50    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
50-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
50-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" /> <!-- Required by older versions of Google Play services to create IID tokens -->
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64cdba80d3b09270f0169dcc68b22b47\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64cdba80d3b09270f0169dcc68b22b47\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
52-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
52-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
53
54    <permission
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
59
60    <application
61        android:name="android.app.Application"
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:icon="@mipmap/ic_launcher"
66        android:label="test2" >
67        <activity
68            android:name="com.example.test2.MainActivity"
69            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
70            android:exported="true"
71            android:hardwareAccelerated="true"
72            android:launchMode="singleTop"
73            android:taskAffinity=""
74            android:theme="@style/LaunchTheme"
75            android:windowSoftInputMode="adjustResize" >
76
77            <!--
78                 Specifies an Android theme to apply to this Activity as soon as
79                 the Android process has started. This theme is visible to the user
80                 while the Flutter UI initializes. After that, this theme continues
81                 to determine the Window background behind the Flutter UI.
82            -->
83            <meta-data
84                android:name="io.flutter.embedding.android.NormalTheme"
85                android:resource="@style/NormalTheme" />
86
87            <intent-filter>
88                <action android:name="android.intent.action.MAIN" />
89
90                <category android:name="android.intent.category.LAUNCHER" />
91            </intent-filter>
92        </activity>
93        <!-- Google Maps API Key (يجب إضافة المفتاح الحقيقي هنا) -->
94        <meta-data
95            android:name="com.google.android.geo.API_KEY"
96            android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE" />
97
98        <!--
99             Don't delete the meta-data below.
100             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
101        -->
102        <meta-data
103            android:name="flutterEmbedding"
104            android:value="2" />
105
106        <service
106-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
107            android:name="com.google.firebase.components.ComponentDiscoveryService"
107-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
108            android:directBootAware="true"
108-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
109            android:exported="false" >
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
110            <meta-data
110-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
111                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
111-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
113            <meta-data
113-->[:firebase_auth] D:\augments_projects\appss\test2\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
114-->[:firebase_auth] D:\augments_projects\appss\test2\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_auth] D:\augments_projects\appss\test2\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
116            <meta-data
116-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
117-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
119            <meta-data
119-->[:firebase_core] D:\augments_projects\appss\test2\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
120                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
120-->[:firebase_core] D:\augments_projects\appss\test2\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[:firebase_core] D:\augments_projects\appss\test2\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
122            <meta-data
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
123                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
123-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
125            <meta-data
125-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
126                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
126-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
131            <meta-data
131-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
132                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
132-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
134            <meta-data
134-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
135                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
135-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
137            <meta-data
137-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
138                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
138-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
140            <meta-data
140-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
141                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
141-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
143            <meta-data
143-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
144                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
144-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
146            <meta-data
146-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7bb0c5cc0af252fff31c8978a6331f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
147                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
147-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7bb0c5cc0af252fff31c8978a6331f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7bb0c5cc0af252fff31c8978a6331f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
149            <meta-data
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
150                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
152            <meta-data
152-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d9eddbce5dae6d7e324aa19ff5324f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
153                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
153-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d9eddbce5dae6d7e324aa19ff5324f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d9eddbce5dae6d7e324aa19ff5324f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
155        </service>
156        <service
156-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
157            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
157-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
158            android:exported="false"
158-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
159            android:permission="android.permission.BIND_JOB_SERVICE" />
159-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
160        <service
160-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
161            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
161-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
162            android:exported="false" >
162-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
163            <intent-filter>
163-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
164                <action android:name="com.google.firebase.MESSAGING_EVENT" />
164-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
164-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
165            </intent-filter>
166        </service>
167
168        <receiver
168-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
169            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
169-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
170            android:exported="true"
170-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
171            android:permission="com.google.android.c2dm.permission.SEND" >
171-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
172            <intent-filter>
172-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
173                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
173-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
173-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
174            </intent-filter>
175        </receiver>
176
177        <provider
177-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
178            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
178-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
179            android:authorities="com.example.test2.flutterfirebasemessaginginitprovider"
179-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
180            android:exported="false"
180-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
181            android:initOrder="99" />
181-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
182
183        <service
183-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
184            android:name="com.baseflow.geolocator.GeolocatorLocationService"
184-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
185            android:enabled="true"
185-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
186            android:exported="false"
186-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
187            android:foregroundServiceType="location" />
187-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
188
189        <activity
189-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
190            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
190-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
191            android:excludeFromRecents="true"
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
192            android:exported="true"
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
193            android:launchMode="singleTask"
193-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
194-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
195            <intent-filter>
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
196                <action android:name="android.intent.action.VIEW" />
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
197
198                <category android:name="android.intent.category.DEFAULT" />
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
199                <category android:name="android.intent.category.BROWSABLE" />
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
200
201                <data
201-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
202                    android:host="firebase.auth"
203                    android:path="/"
204                    android:scheme="genericidp" />
205            </intent-filter>
206        </activity>
207        <activity
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
208            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
209            android:excludeFromRecents="true"
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
210            android:exported="true"
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
211            android:launchMode="singleTask"
211-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
212            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
212-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
213            <intent-filter>
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
214                <action android:name="android.intent.action.VIEW" />
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
215
216                <category android:name="android.intent.category.DEFAULT" />
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
217                <category android:name="android.intent.category.BROWSABLE" />
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
218
219                <data
219-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
220                    android:host="firebase.auth"
221                    android:path="/"
222                    android:scheme="recaptcha" />
223            </intent-filter>
224        </activity>
225
226        <provider
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
227            android:name="androidx.startup.InitializationProvider"
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
228            android:authorities="com.example.test2.androidx-startup"
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
229            android:exported="false" >
229-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
230            <meta-data
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
231                android:name="androidx.work.WorkManagerInitializer"
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
232                android:value="androidx.startup" />
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
233            <meta-data
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
234                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
235                android:value="androidx.startup" />
235-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
236        </provider>
237
238        <service
238-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
239            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
239-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
240            android:directBootAware="false"
240-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
241            android:enabled="@bool/enable_system_alarm_service_default"
241-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
242            android:exported="false" />
242-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
243        <service
243-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
244            android:name="androidx.work.impl.background.systemjob.SystemJobService"
244-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
245            android:directBootAware="false"
245-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
246            android:enabled="@bool/enable_system_job_service_default"
246-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
247            android:exported="true"
247-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
248            android:permission="android.permission.BIND_JOB_SERVICE" />
248-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
249        <service
249-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
250            android:name="androidx.work.impl.foreground.SystemForegroundService"
250-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
251            android:directBootAware="false"
251-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
252            android:enabled="@bool/enable_system_foreground_service_default"
252-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
253            android:exported="false" />
253-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
254
255        <receiver
255-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
256            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
256-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
257            android:directBootAware="false"
257-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
258            android:enabled="true"
258-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
259            android:exported="false" />
259-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
260        <receiver
260-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
261            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
263            android:enabled="false"
263-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
264            android:exported="false" >
264-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
265            <intent-filter>
265-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
266                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
266-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
266-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
267                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
267-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
267-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
268            </intent-filter>
269        </receiver>
270        <receiver
270-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
271            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
271-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
273            android:enabled="false"
273-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
274            android:exported="false" >
274-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
275            <intent-filter>
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
276                <action android:name="android.intent.action.BATTERY_OKAY" />
276-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
276-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
277                <action android:name="android.intent.action.BATTERY_LOW" />
277-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
277-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
278            </intent-filter>
279        </receiver>
280        <receiver
280-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
281            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
281-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
286                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
287                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
287-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
287-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
288            </intent-filter>
289        </receiver>
290        <receiver
290-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
291            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
291-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
293            android:enabled="false"
293-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
294            android:exported="false" >
294-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
295            <intent-filter>
295-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
296                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
300            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
300-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
302            android:enabled="false"
302-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
303            android:exported="false" >
303-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
304            <intent-filter>
304-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
305                <action android:name="android.intent.action.BOOT_COMPLETED" />
305-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
305-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
306                <action android:name="android.intent.action.TIME_SET" />
306-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
306-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
307                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
308            </intent-filter>
309        </receiver>
310        <receiver
310-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
311            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
311-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
313            android:enabled="@bool/enable_system_alarm_service_default"
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
314            android:exported="false" >
314-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
315            <intent-filter>
315-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
316                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
320            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
320-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
322            android:enabled="true"
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
323            android:exported="true"
323-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
324            android:permission="android.permission.DUMP" >
324-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
325            <intent-filter>
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
326                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
326-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
326-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
327            </intent-filter>
328        </receiver> <!-- Needs to be explicitly declared on P+ -->
329        <uses-library
329-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
330            android:name="org.apache.http.legacy"
330-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
331            android:required="false" />
331-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
332
333        <receiver
333-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
334            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
334-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
335            android:enabled="true"
335-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
336            android:exported="false" >
336-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
337        </receiver>
338
339        <service
339-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
340            android:name="com.google.android.gms.measurement.AppMeasurementService"
340-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
341            android:enabled="true"
341-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
342            android:exported="false" />
342-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
343        <service
343-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
344            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
344-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
345            android:enabled="true"
345-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
346            android:exported="false"
346-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
347            android:permission="android.permission.BIND_JOB_SERVICE" />
347-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
348        <service
348-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
349            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
349-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
350            android:enabled="true"
350-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
351            android:exported="false" >
351-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
352            <meta-data
352-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
353                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
353-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
354                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
354-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
355        </service>
356
357        <activity
357-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
358            android:name="androidx.credentials.playservices.HiddenActivity"
358-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
359            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
359-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
360            android:enabled="true"
360-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
361            android:exported="false"
361-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
362            android:fitsSystemWindows="true"
362-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
363            android:theme="@style/Theme.Hidden" >
363-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
364        </activity>
365        <activity
365-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
366            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
366-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
367            android:excludeFromRecents="true"
367-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
368            android:exported="false"
368-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
369            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
369-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
370        <!--
371            Service handling Google Sign-In user revocation. For apps that do not integrate with
372            Google Sign-In, this service will never be started.
373        -->
374        <service
374-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
375            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
375-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
376            android:exported="true"
376-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
377            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
377-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
378            android:visibleToInstantApps="true" />
378-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
379
380        <receiver
380-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
381            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
381-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
382            android:exported="true"
382-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
383            android:permission="com.google.android.c2dm.permission.SEND" >
383-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
384            <intent-filter>
384-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
385                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
385-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
385-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
386            </intent-filter>
387
388            <meta-data
388-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
389                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
389-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
390                android:value="true" />
390-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
391        </receiver>
392        <!--
393             FirebaseMessagingService performs security checks at runtime,
394             but set to not exported to explicitly avoid allowing another app to call it.
395        -->
396        <service
396-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
397            android:name="com.google.firebase.messaging.FirebaseMessagingService"
397-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
398            android:directBootAware="true"
398-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
399            android:exported="false" >
399-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
400            <intent-filter android:priority="-500" >
400-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
401                <action android:name="com.google.firebase.MESSAGING_EVENT" />
401-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
401-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
402            </intent-filter>
403        </service>
404
405        <provider
405-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
406            android:name="com.google.firebase.provider.FirebaseInitProvider"
406-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
407            android:authorities="com.example.test2.firebaseinitprovider"
407-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
408            android:directBootAware="true"
408-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
409            android:exported="false"
409-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
410            android:initOrder="100" />
410-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
411
412        <uses-library
412-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
413            android:name="androidx.window.extensions"
413-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
414            android:required="false" />
414-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
415        <uses-library
415-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
416            android:name="androidx.window.sidecar"
416-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
417            android:required="false" />
417-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
418        <uses-library
418-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
419            android:name="android.ext.adservices"
419-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
420            android:required="false" />
420-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
421
422        <activity
422-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
423            android:name="com.google.android.gms.common.api.GoogleApiActivity"
423-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
424            android:exported="false"
424-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
425            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
425-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
426
427        <meta-data
427-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
428            android:name="com.google.android.gms.version"
428-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
429            android:value="@integer/google_play_services_version" />
429-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
430
431        <service
431-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
432            android:name="androidx.room.MultiInstanceInvalidationService"
432-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
433            android:directBootAware="true"
433-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
434            android:exported="false" />
434-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
435
436        <receiver
436-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
437            android:name="androidx.profileinstaller.ProfileInstallReceiver"
437-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
438            android:directBootAware="false"
438-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
439            android:enabled="true"
439-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
440            android:exported="true"
440-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
441            android:permission="android.permission.DUMP" >
441-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
442            <intent-filter>
442-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
443                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
443-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
443-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
444            </intent-filter>
445            <intent-filter>
445-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
446                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
446-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
446-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
447            </intent-filter>
448            <intent-filter>
448-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
449                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
449-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
449-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
450            </intent-filter>
451            <intent-filter>
451-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
452                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
452-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
452-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
453            </intent-filter>
454        </receiver>
455
456        <service
456-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
457            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
457-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
458            android:exported="false" >
458-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
459            <meta-data
459-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
460                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
460-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
461                android:value="cct" />
461-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
462        </service>
463        <service
463-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
464            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
464-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
465            android:exported="false"
465-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
466            android:permission="android.permission.BIND_JOB_SERVICE" >
466-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
467        </service>
468
469        <receiver
469-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
470            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
470-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
471            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
471-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
472        <activity
472-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
473            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
473-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
474            android:exported="false"
474-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
475            android:stateNotNeeded="true"
475-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
476            android:theme="@style/Theme.PlayCore.Transparent" />
476-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
477    </application>
478
479</manifest>
