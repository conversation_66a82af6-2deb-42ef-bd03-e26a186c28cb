1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.test2"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:6:5-67
15-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:4:5-79
17-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:4:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- إذن للوصول لحالة الشبكة -->
18-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:5:5-81
18-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:5:22-78
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:9:5-79
19-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:9:22-76
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:52:5-57:15
28        <intent>
28-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:53:9-56:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:54:13-72
29-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:54:21-70
30
31            <data android:mimeType="text/plain" />
31-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
31-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:19-48
32        </intent>
33        <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
37-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
37-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-65
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
38-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
38-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
39    <uses-permission android:name="android.permission.VIBRATE" />
39-->[:flutter_local_notifications] D:\augments_projects\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
39-->[:flutter_local_notifications] D:\augments_projects\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
40    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
42
43    <uses-feature
43-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
44        android:glEsVersion="0x00020000"
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
45        android:required="true" />
45-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
46
47    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
47-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
47-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
48    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
48-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
49    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
49-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
49-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
50    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
50-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
50-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" /> <!-- Required by older versions of Google Play services to create IID tokens -->
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64cdba80d3b09270f0169dcc68b22b47\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64cdba80d3b09270f0169dcc68b22b47\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
52-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
52-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
53
54    <permission
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
59
60    <application
61        android:name="android.app.Application"
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\3927d342be7108a61372589dd032352d\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:icon="@mipmap/ic_launcher"
66        android:label="test2" >
67        <activity
68            android:name="com.example.test2.MainActivity"
69            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
70            android:exported="true"
71            android:hardwareAccelerated="true"
72            android:launchMode="singleTop"
73            android:taskAffinity=""
74            android:theme="@style/LaunchTheme"
75            android:windowSoftInputMode="adjustResize" >
76
77            <!--
78                 Specifies an Android theme to apply to this Activity as soon as
79                 the Android process has started. This theme is visible to the user
80                 while the Flutter UI initializes. After that, this theme continues
81                 to determine the Window background behind the Flutter UI.
82            -->
83            <meta-data
84                android:name="io.flutter.embedding.android.NormalTheme"
85                android:resource="@style/NormalTheme" />
86
87            <intent-filter>
88                <action android:name="android.intent.action.MAIN" />
89
90                <category android:name="android.intent.category.LAUNCHER" />
91            </intent-filter>
92        </activity>
93        <!-- Google Maps API Key (يجب إضافة المفتاح الحقيقي هنا) -->
94        <meta-data
95            android:name="com.google.android.geo.API_KEY"
96            android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE" />
97
98        <!--
99             Don't delete the meta-data below.
100             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
101        -->
102        <meta-data
103            android:name="flutterEmbedding"
104            android:value="2" />
105
106        <service
106-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
107            android:name="com.google.firebase.components.ComponentDiscoveryService"
107-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
108            android:directBootAware="true"
108-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
109            android:exported="false" >
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
110            <meta-data
110-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
111                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
111-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[:cloud_firestore] D:\augments_projects\appss\test2\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
113            <meta-data
113-->[:firebase_auth] D:\augments_projects\appss\test2\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
114-->[:firebase_auth] D:\augments_projects\appss\test2\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_auth] D:\augments_projects\appss\test2\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
116            <meta-data
116-->[:firebase_database] D:\augments_projects\appss\test2\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
117-->[:firebase_database] D:\augments_projects\appss\test2\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-127
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_database] D:\augments_projects\appss\test2\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
119            <meta-data
119-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
120                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
120-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
122            <meta-data
122-->[:firebase_core] D:\augments_projects\appss\test2\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
123                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
123-->[:firebase_core] D:\augments_projects\appss\test2\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[:firebase_core] D:\augments_projects\appss\test2\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
125            <meta-data
125-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
126                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
126-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
131            <meta-data
131-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
132                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5f89eb98d826597d278d0f004aede5e\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
134            <meta-data
134-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
135                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
135-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\1462f49d23cd904c99409c09ba24af9c\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
137            <meta-data
137-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e31147d4efa695e00a83bed64f3918\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
138                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
138-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e31147d4efa695e00a83bed64f3918\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e31147d4efa695e00a83bed64f3918\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
140            <meta-data
140-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e31147d4efa695e00a83bed64f3918\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
141                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
141-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e31147d4efa695e00a83bed64f3918\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e31147d4efa695e00a83bed64f3918\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
143            <meta-data
143-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
144                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
144-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
146            <meta-data
146-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
147                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
147-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
149            <meta-data
149-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
150                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
152            <meta-data
152-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
153                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
153-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b537eaf3ad9cd2402dd60c1e41af3d24\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
155            <meta-data
155-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7bb0c5cc0af252fff31c8978a6331f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
156                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
156-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7bb0c5cc0af252fff31c8978a6331f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7bb0c5cc0af252fff31c8978a6331f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
158            <meta-data
158-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
159                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
159-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
161            <meta-data
161-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d9eddbce5dae6d7e324aa19ff5324f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
162                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
162-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d9eddbce5dae6d7e324aa19ff5324f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d9eddbce5dae6d7e324aa19ff5324f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
164        </service>
165        <service
165-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
166            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
166-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
167            android:exported="false"
167-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
168            android:permission="android.permission.BIND_JOB_SERVICE" />
168-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
169        <service
169-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
170            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
170-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
171            android:exported="false" >
171-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
172            <intent-filter>
172-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
173                <action android:name="com.google.firebase.MESSAGING_EVENT" />
173-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
173-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
174            </intent-filter>
175        </service>
176
177        <receiver
177-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
178            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
178-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
179            android:exported="true"
179-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
180            android:permission="com.google.android.c2dm.permission.SEND" >
180-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
181            <intent-filter>
181-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
182                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
182-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
182-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
183            </intent-filter>
184        </receiver>
185
186        <provider
186-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
187            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
187-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
188            android:authorities="com.example.test2.flutterfirebasemessaginginitprovider"
188-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
189            android:exported="false"
189-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
190            android:initOrder="99" />
190-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
191
192        <service
192-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
193            android:name="com.baseflow.geolocator.GeolocatorLocationService"
193-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
194            android:enabled="true"
194-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
195            android:exported="false"
195-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
196            android:foregroundServiceType="location" />
196-->[:geolocator_android] D:\augments_projects\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
197
198        <activity
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
199            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
200            android:excludeFromRecents="true"
200-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
201            android:exported="true"
201-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
202            android:launchMode="singleTask"
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
203            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
203-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
204            <intent-filter>
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
205                <action android:name="android.intent.action.VIEW" />
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
206
207                <category android:name="android.intent.category.DEFAULT" />
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
208                <category android:name="android.intent.category.BROWSABLE" />
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
209
210                <data
210-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
211                    android:host="firebase.auth"
212                    android:path="/"
213                    android:scheme="genericidp" />
214            </intent-filter>
215        </activity>
216        <activity
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
217            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
218            android:excludeFromRecents="true"
218-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
219            android:exported="true"
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
220            android:launchMode="singleTask"
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
222            <intent-filter>
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
223                <action android:name="android.intent.action.VIEW" />
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
224
225                <category android:name="android.intent.category.DEFAULT" />
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
226                <category android:name="android.intent.category.BROWSABLE" />
226-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
226-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a612cde3222e3d2041e153cfde646845\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
227
228                <data
228-->D:\augments_projects\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
229                    android:host="firebase.auth"
230                    android:path="/"
231                    android:scheme="recaptcha" />
232            </intent-filter>
233        </activity>
234
235        <provider
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
236            android:name="androidx.startup.InitializationProvider"
236-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
237            android:authorities="com.example.test2.androidx-startup"
237-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
238            android:exported="false" >
238-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
239            <meta-data
239-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
240                android:name="androidx.work.WorkManagerInitializer"
240-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
241                android:value="androidx.startup" />
241-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
242            <meta-data
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
243                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
244                android:value="androidx.startup" />
244-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
245        </provider>
246
247        <service
247-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
248            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
248-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
250            android:enabled="@bool/enable_system_alarm_service_default"
250-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
251            android:exported="false" />
251-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
252        <service
252-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
253            android:name="androidx.work.impl.background.systemjob.SystemJobService"
253-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
254            android:directBootAware="false"
254-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
255            android:enabled="@bool/enable_system_job_service_default"
255-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
256            android:exported="true"
256-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
257            android:permission="android.permission.BIND_JOB_SERVICE" />
257-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
258        <service
258-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
259            android:name="androidx.work.impl.foreground.SystemForegroundService"
259-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
261            android:enabled="@bool/enable_system_foreground_service_default"
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
262            android:exported="false" />
262-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
263
264        <receiver
264-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
265            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
265-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
267            android:enabled="true"
267-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
268            android:exported="false" />
268-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
269        <receiver
269-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
270-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
275                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
276                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
276-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
276-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
277            </intent-filter>
278        </receiver>
279        <receiver
279-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
280            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
280-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
285                <action android:name="android.intent.action.BATTERY_OKAY" />
285-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
285-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
286                <action android:name="android.intent.action.BATTERY_LOW" />
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
290-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
295                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
295-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
295-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
296                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
300            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
300-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
302            android:enabled="false"
302-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
303            android:exported="false" >
303-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
304            <intent-filter>
304-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
305                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
305-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
305-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
306            </intent-filter>
307        </receiver>
308        <receiver
308-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
309            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
309-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
310            android:directBootAware="false"
310-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
311            android:enabled="false"
311-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
312            android:exported="false" >
312-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
313            <intent-filter>
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
314                <action android:name="android.intent.action.BOOT_COMPLETED" />
314-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
314-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
315                <action android:name="android.intent.action.TIME_SET" />
315-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
315-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
316                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
316-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
320            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
320-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
322            android:enabled="@bool/enable_system_alarm_service_default"
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
323            android:exported="false" >
323-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
324            <intent-filter>
324-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
325                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
326            </intent-filter>
327        </receiver>
328        <receiver
328-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
329            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
329-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
330            android:directBootAware="false"
330-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
331            android:enabled="true"
331-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
332            android:exported="true"
332-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
333            android:permission="android.permission.DUMP" >
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
334            <intent-filter>
334-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
335                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
335-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
335-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\af735b56e9cba6b1942a99866ff67516\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
336            </intent-filter>
337        </receiver> <!-- Needs to be explicitly declared on P+ -->
338        <uses-library
338-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
339            android:name="org.apache.http.legacy"
339-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
340            android:required="false" />
340-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e801fb926a3d574c06ff316d2665fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
341
342        <receiver
342-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
343            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
343-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
344            android:enabled="true"
344-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
345            android:exported="false" >
345-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
346        </receiver>
347
348        <service
348-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
349            android:name="com.google.android.gms.measurement.AppMeasurementService"
349-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
350            android:enabled="true"
350-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
351            android:exported="false" />
351-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
352        <service
352-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
353            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
353-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
354            android:enabled="true"
354-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
355            android:exported="false"
355-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
356            android:permission="android.permission.BIND_JOB_SERVICE" />
356-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2dc3638a215bf7fd363473bddb141bc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
357        <service
357-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
358            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
358-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
359            android:enabled="true"
359-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
360            android:exported="false" >
360-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
361            <meta-data
361-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
362                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
362-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
363                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
363-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
364        </service>
365
366        <activity
366-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
367            android:name="androidx.credentials.playservices.HiddenActivity"
367-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
368            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
368-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
369            android:enabled="true"
369-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
370            android:exported="false"
370-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
371            android:fitsSystemWindows="true"
371-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
372            android:theme="@style/Theme.Hidden" >
372-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\4aca9238de64e93789858c4e0fabfeba\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
373        </activity>
374        <activity
374-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
375            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
375-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
376            android:excludeFromRecents="true"
376-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
377            android:exported="false"
377-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
378            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
378-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
379        <!--
380            Service handling Google Sign-In user revocation. For apps that do not integrate with
381            Google Sign-In, this service will never be started.
382        -->
383        <service
383-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
384            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
384-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
385            android:exported="true"
385-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
386            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
386-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
387            android:visibleToInstantApps="true" />
387-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f734402fc9fea8952627063e77117bfd\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
388
389        <receiver
389-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
390            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
390-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
391            android:exported="true"
391-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
392            android:permission="com.google.android.c2dm.permission.SEND" >
392-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
393            <intent-filter>
393-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
394                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
394-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
394-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
395            </intent-filter>
396
397            <meta-data
397-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
398                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
398-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
399                android:value="true" />
399-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
400        </receiver>
401        <!--
402             FirebaseMessagingService performs security checks at runtime,
403             but set to not exported to explicitly avoid allowing another app to call it.
404        -->
405        <service
405-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
406            android:name="com.google.firebase.messaging.FirebaseMessagingService"
406-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
407            android:directBootAware="true"
407-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
408            android:exported="false" >
408-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10eaf2ffd214251ac1191e292ccbd398\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
409            <intent-filter android:priority="-500" >
409-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
410                <action android:name="com.google.firebase.MESSAGING_EVENT" />
410-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
410-->[:firebase_messaging] D:\augments_projects\appss\test2\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
411            </intent-filter>
412        </service>
413
414        <provider
414-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
415            android:name="com.google.firebase.provider.FirebaseInitProvider"
415-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
416            android:authorities="com.example.test2.firebaseinitprovider"
416-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
417            android:directBootAware="true"
417-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
418            android:exported="false"
418-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
419            android:initOrder="100" />
419-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e36f97ec291e07cd0d99aeed010a1ac\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
420
421        <uses-library
421-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
422            android:name="androidx.window.extensions"
422-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
423            android:required="false" />
423-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
424        <uses-library
424-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
425            android:name="androidx.window.sidecar"
425-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
426            android:required="false" />
426-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\4331d38e1b5b1898f63e3abba3c0c6d2\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
427        <uses-library
427-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
428            android:name="android.ext.adservices"
428-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
429            android:required="false" />
429-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\5d01b73a4c35472c3e887fea749a3154\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
430
431        <activity
431-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
432            android:name="com.google.android.gms.common.api.GoogleApiActivity"
432-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
433            android:exported="false"
433-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
434            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
434-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed293fa1fecc38363e0267349aa1d19f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
435
436        <meta-data
436-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
437            android:name="com.google.android.gms.version"
437-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
438            android:value="@integer/google_play_services_version" />
438-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\403f29e94df891a2dd1d4561277e8087\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
439
440        <service
440-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
441            android:name="androidx.room.MultiInstanceInvalidationService"
441-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
442            android:directBootAware="true"
442-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
443            android:exported="false" />
443-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\16cb44bce73fa407cff8f48da79d94f7\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
444
445        <receiver
445-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
446            android:name="androidx.profileinstaller.ProfileInstallReceiver"
446-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
447            android:directBootAware="false"
447-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
448            android:enabled="true"
448-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
449            android:exported="true"
449-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
450            android:permission="android.permission.DUMP" >
450-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
451            <intent-filter>
451-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
452                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
452-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
452-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
453            </intent-filter>
454            <intent-filter>
454-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
455                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
455-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
455-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
456            </intent-filter>
457            <intent-filter>
457-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
458                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
458-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
458-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
459            </intent-filter>
460            <intent-filter>
460-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
461                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
461-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
461-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea117ad5fb9cf854e7d016d333d9c54a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
462            </intent-filter>
463        </receiver>
464
465        <service
465-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
466            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
466-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
467            android:exported="false" >
467-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
468            <meta-data
468-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
469                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
469-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
470                android:value="cct" />
470-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5a10492e5e72c34125bd415312d13faf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
471        </service>
472        <service
472-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
473            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
473-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
474            android:exported="false"
474-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
475            android:permission="android.permission.BIND_JOB_SERVICE" >
475-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
476        </service>
477
478        <receiver
478-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
479            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
479-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
480            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
480-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\19decd735d1e7de01b948fb6240ca76d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
481        <activity
481-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
482            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
482-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
483            android:exported="false"
483-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
484            android:stateNotNeeded="true"
484-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
485            android:theme="@style/Theme.PlayCore.Transparent" />
485-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\439e89e84779b59d76ae02cb100103cf\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
486    </application>
487
488</manifest>
