# import firebase_admin
# from firebase_admin import credentials, db

# # 🔐 تحميل بيانات المصادقة
# cred = credentials.Certificate("serviceAccountKey.json")

# # 🌐 ربط المشروع بقاعدة البيانات
# firebase_admin.initialize_app(cred, {
#     'databaseURL': 'https://zad-k22-default-rtdb.asia-southeast1.firebasedatabase.app/'  # ← غيّره
# })

# # ✏️ مرجع المسار الذي ستُرسل إليه البيانات
# ref = db.reference("orders")

# # ✅ إرسال بيانات جديدة (مثال: طلب جديد)
# new_order = {
#     "clientId": "user_123",
#     "restaurantId": "rest_456",
#     "driverId": "driver_789",
#     "status": "pending",
#     "items": ["برجر", "عصير"],
#     "createdAt": "2025-06-21 18:00:00"
# }

# # 📨 إرسال البيانات إلى Firebase
# ref.push(new_order)

# print("✅ تم إرسال الطلب إلى Firebase")


import firebase_admin
from firebase_admin import credentials, db

# تهيئة الاتصال بـ Firebase
cred = credentials.Certificate("zad-k22-firebase-adminsdk-fbsvc-2be9e6fb69.json")
firebase_admin.initialize_app(cred, {
    'databaseURL': 'https://zad-k22-default-rtdb.asia-southeast1.firebasedatabase.app/'
})

# 👇 معرف مطعم الشيباني (يجب أن يكون ثابتًا في قاعدة البيانات)
restaurant_id = "rest_alshaybani"

# 📌 المسار الخاص بمطعم الشيباني فقط
order_ref = db.reference(f"orderss/{restaurant_id}")

# 🔽 بيانات الطلب المُرسلة
order_data = {
    "orderId": "ORD-001",
    "clientId": "user_123",
    "items": ["كباب", "ماء"],
    "status": "pending",
    "createdAt": "2025-06-21 18:15:00"
}

# إرسال الطلب إلى مطعم الشيباني فقط
order_ref.push(order_data)

print(f"✅ تم إرسال الطلب إلى {restaurant_id}")
