import 'dart:async';
import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Notification.dart' as AppNotification;
import '../services/NotificationService.dart';
import '../services/CustomerDataService.dart';

/// خدمة Firebase Cloud Messaging للإشعارات الفورية والمؤجلة
class FirebaseMessagingService {
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final NotificationService _notificationService = NotificationService();
  final CustomerDataService _customerService = CustomerDataService();
  
  static const String _queuedNotificationsKey = 'queued_notifications';
  static const String _lastProcessedKey = 'last_processed_notification';
  
  bool _isInitialized = false;
  String? _fcmToken;

  /// تهيئة خدمة Firebase Messaging
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // طلب إذن الإشعارات
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('تم منح إذن الإشعارات');
        
        // الحصول على FCM Token
        _fcmToken = await _messaging.getToken();
        print('FCM Token: $_fcmToken');
        
        // حفظ التوكن في Firebase للمستخدم
        await _saveFCMTokenToFirestore();
        
        // إعداد معالجات الإشعارات
        _setupMessageHandlers();
        
        // معالجة الإشعارات المؤجلة عند عودة الاتصال
        await _processQueuedNotifications();
        
        _isInitialized = true;
        print('تم تهيئة Firebase Messaging بنجاح');
      } else {
        print('لم يتم منح إذن الإشعارات');
      }
    } catch (e) {
      print('خطأ في تهيئة Firebase Messaging: $e');
    }
  }

  /// إعداد معالجات الإشعارات
  void _setupMessageHandlers() {
    // معالجة الإشعارات عندما يكون التطبيق في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // معالجة الإشعارات عندما يكون التطبيق في الخلفية ويتم فتحه
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
    
    // معالجة الإشعارات عند فتح التطبيق من حالة مغلقة
    _handleInitialMessage();
  }

  /// معالجة الإشعارات في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('تم استلام إشعار في المقدمة: ${message.messageId}');
    await _processRemoteMessage(message);
  }

  /// معالجة الإشعارات في الخلفية
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('تم فتح التطبيق من إشعار: ${message.messageId}');
    await _processRemoteMessage(message);
  }

  /// معالجة الإشعار الأولي
  Future<void> _handleInitialMessage() async {
    RemoteMessage? initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      print('تم فتح التطبيق من إشعار أولي: ${initialMessage.messageId}');
      await _processRemoteMessage(initialMessage);
    }
  }

  /// معالجة الرسالة البعيدة
  Future<void> _processRemoteMessage(RemoteMessage message) async {
    try {
      final data = message.data;
      final notification = message.notification;
      
      if (data.isNotEmpty) {
        // تحديد نوع الإشعار
        final notificationType = _getNotificationTypeFromData(data);
        
        // إنشاء إشعار محلي
        await _notificationService.addNotification(
          title: notification?.title ?? data['title'] ?? 'تحديث جديد',
          message: notification?.body ?? data['message'] ?? '',
          type: notificationType,
          data: data,
        );
        
        // تسجيل معالجة الإشعار
        await _markNotificationAsProcessed(message.messageId ?? '');
      }
    } catch (e) {
      print('خطأ في معالجة الرسالة البعيدة: $e');
    }
  }

  /// تحديد نوع الإشعار من البيانات
  AppNotification.NotificationType _getNotificationTypeFromData(Map<String, dynamic> data) {
    final type = data['type']?.toString().toLowerCase();

    switch (type) {
      case 'order_confirmed':
        return AppNotification.NotificationType.orderConfirmed;
      case 'order_preparing':
        return AppNotification.NotificationType.orderPreparing;
      case 'order_ready':
        return AppNotification.NotificationType.orderReady;
      case 'order_delivered':
        return AppNotification.NotificationType.orderDelivered;
      case 'offer':
        return AppNotification.NotificationType.offer;
      case 'new_product':
        return AppNotification.NotificationType.newProduct;
      case 'new_store':
        return AppNotification.NotificationType.newStore;
      default:
        return AppNotification.NotificationType.general;
    }
  }

  /// حفظ FCM Token في Firestore
  Future<void> _saveFCMTokenToFirestore() async {
    try {
      await _customerService.ensureDataLoaded();
      final userId = _customerService.getCustomerId();
      
      if (userId.isNotEmpty && _fcmToken != null) {
        await _firestore
            .collection('users')
            .doc(userId)
            .update({
          'fcmToken': _fcmToken,
          'lastTokenUpdate': DateTime.now().toIso8601String(),
          'platform': 'flutter',
        });
        
        print('تم حفظ FCM Token للمستخدم: $userId');
      }
    } catch (e) {
      print('خطأ في حفظ FCM Token: $e');
    }
  }

  /// إضافة إشعار إلى قائمة الانتظار للمعالجة لاحقاً
  Future<void> queueNotificationForLater(Map<String, dynamic> notificationData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queuedNotifications = prefs.getStringList(_queuedNotificationsKey) ?? [];
      
      // إضافة timestamp للإشعار
      notificationData['queuedAt'] = DateTime.now().toIso8601String();
      
      queuedNotifications.add(jsonEncode(notificationData));
      await prefs.setStringList(_queuedNotificationsKey, queuedNotifications);
      
      print('تم إضافة إشعار إلى قائمة الانتظار: ${notificationData['title']}');
    } catch (e) {
      print('خطأ في إضافة إشعار إلى قائمة الانتظار: $e');
    }
  }

  /// معالجة الإشعارات المؤجلة عند عودة الاتصال
  Future<void> _processQueuedNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queuedNotifications = prefs.getStringList(_queuedNotificationsKey) ?? [];
      
      if (queuedNotifications.isEmpty) return;
      
      print('معالجة ${queuedNotifications.length} إشعار مؤجل');
      
      for (final notificationJson in queuedNotifications) {
        try {
          final notificationData = jsonDecode(notificationJson) as Map<String, dynamic>;
          
          // التحقق من أن الإشعار لم يتم معالجته من قبل
          if (!await _isNotificationProcessed(notificationData['id'])) {
            await _processQueuedNotification(notificationData);
            await _markNotificationAsProcessed(notificationData['id']);
          }
        } catch (e) {
          print('خطأ في معالجة إشعار مؤجل: $e');
        }
      }
      
      // مسح قائمة الإشعارات المؤجلة
      await prefs.remove(_queuedNotificationsKey);
      print('تم مسح قائمة الإشعارات المؤجلة');
      
    } catch (e) {
      print('خطأ في معالجة الإشعارات المؤجلة: $e');
    }
  }

  /// معالجة إشعار مؤجل
  Future<void> _processQueuedNotification(Map<String, dynamic> data) async {
    try {
      final notificationType = _getNotificationTypeFromData(data);
      
      await _notificationService.addNotification(
        title: data['title'] ?? 'تحديث جديد',
        message: data['message'] ?? '',
        type: notificationType,
        data: data,
      );
      
      print('تم معالجة إشعار مؤجل: ${data['title']}');
    } catch (e) {
      print('خطأ في معالجة إشعار مؤجل: $e');
    }
  }

  /// التحقق من معالجة الإشعار سابقاً
  Future<bool> _isNotificationProcessed(String? notificationId) async {
    if (notificationId == null) return false;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedNotifications = prefs.getStringList(_lastProcessedKey) ?? [];
      return processedNotifications.contains(notificationId);
    } catch (e) {
      return false;
    }
  }

  /// تسجيل معالجة الإشعار
  Future<void> _markNotificationAsProcessed(String notificationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedNotifications = prefs.getStringList(_lastProcessedKey) ?? [];
      
      if (!processedNotifications.contains(notificationId)) {
        processedNotifications.add(notificationId);
        
        // الاحتفاظ بآخر 100 إشعار فقط لتوفير المساحة
        if (processedNotifications.length > 100) {
          processedNotifications.removeRange(0, processedNotifications.length - 100);
        }
        
        await prefs.setStringList(_lastProcessedKey, processedNotifications);
      }
    } catch (e) {
      print('خطأ في تسجيل معالجة الإشعار: $e');
    }
  }

  /// الحصول على FCM Token
  String? get fcmToken => _fcmToken;

  /// تحديث FCM Token
  Future<void> refreshToken() async {
    try {
      _fcmToken = await _messaging.getToken();
      await _saveFCMTokenToFirestore();
      print('تم تحديث FCM Token');
    } catch (e) {
      print('خطأ في تحديث FCM Token: $e');
    }
  }

  /// إلغاء الاشتراك في موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging.unsubscribeFromTopic(topic);
      print('تم إلغاء الاشتراك في الموضوع: $topic');
    } catch (e) {
      print('خطأ في إلغاء الاشتراك: $e');
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging.subscribeToTopic(topic);
      print('تم الاشتراك في الموضوع: $topic');
    } catch (e) {
      print('خطأ في الاشتراك: $e');
    }
  }
}
