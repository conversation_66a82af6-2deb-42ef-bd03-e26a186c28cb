import 'package:firebase_auth/firebase_auth.dart';

class AuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  // المستخدم الحالي
  User? get currentUser => _firebaseAuth.currentUser;

  // البث لحالة تسجيل الدخول
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // تخزين معرف التحقق
  String? _verificationId;

  /// إرسال كود التحقق
  Future<void> sendOtp({
    required String phoneNumber,
    required Function(String verificationId) onCodeSent,
    required Function(FirebaseAuthException e) onFailed,
    required Function(PhoneAuthCredential credential) onAutoVerified,
  }) async {
    await _firebaseAuth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      timeout: const Duration(seconds: 60),
      verificationCompleted: onAutoVerified,
      verificationFailed: onFailed,
      codeSent: (String verificationId, int? resendToken) {
        _verificationId = verificationId;
        onCodeSent(verificationId);
      },
      codeAutoRetrievalTimeout: (String verificationId) {
        _verificationId = verificationId;
      },
    );
  }

  /// التحقق من الكود الذي أدخله المستخدم
  Future<void> verifyOtp({
    required String smsCode,
  }) async {
    if (_verificationId == null) {
      throw Exception("No verification ID found. Did you send the code?");
    }

    PhoneAuthCredential credential = PhoneAuthProvider.credential(
      verificationId: _verificationId!,
      smsCode: smsCode,
    );

    await _firebaseAuth.signInWithCredential(credential);
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }
}
