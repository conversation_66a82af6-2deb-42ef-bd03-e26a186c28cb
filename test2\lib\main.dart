import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/pages/CartPage.dart';
import 'package:test2/pages/HomePages.dart';
import 'package:test2/pages/ItemsPages.dart';
import 'package:test2/pages/MyOrdersPage.dart';
import 'package:test2/pages/OrderDetailsPage.dart';
import 'package:test2/pages/OrderTrackingPage.dart';
import 'package:test2/pages/StoresPage.dart';
import 'package:test2/pages/SplashScreen.dart';
import 'package:test2/pages/ProfilePage.dart';
import 'package:test2/pages/SettingsPage.dart';
import 'package:test2/pages/LoginPage.dart';
import 'package:test2/pages/RegisterPage.dart';
import 'package:test2/pages/FavoritesPage.dart';
import 'package:test2/pages/AddressesPage.dart';
import 'package:test2/pages/OffersPage.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/services/SettingsService.dart'; // استيراد خدمة الإعدادات
import 'package:test2/services/NotificationService.dart'; // استيراد خدمة الإشعارات

import 'package:test2/providers/CartProvider.dart'; // استيراد مزود السلة

import 'package:test2/providers/OrderProvider.dart'; // استيراد مزود الطلبات
import 'package:test2/providers/CustomerDataProvider.dart'; // استيراد مزود بيانات العميل
import 'package:test2/providers/AuthenticationProvider.dart'; // استيراد مزود المصادقة
import 'package:test2/providers/SearchProvider.dart'; // استيراد مزود البحث
import 'package:test2/providers/NotificationProvider.dart'; // استيراد مزود الإشعارات الموحد
import 'package:test2/providers/AddressProvider.dart'; // استيراد مزود العناوين
import 'package:test2/utils/PerformanceOptimizer.dart';


import 'package:firebase_core/firebase_core.dart';
// import 'package:test2/PhoneAuthScreen.dart';

void main() async {
  WidgetsFlutterBinding
      .ensureInitialized(); // التأكد من تهيئة Flutter قبل تشغيل التطبيق

  await Firebase.initializeApp(); // ← هذا هو الأهم
  // تطبيق تحسينات الأداء
  PerformanceOptimizer.optimizeApp();

  // تهيئة خدمة الإشعارات
  await NotificationService().initialize();

  // تحميل إعدادات التطبيق من التخزين المحلي
  final settingsService = SettingsService(); // إنشاء مثيل من خدمة الإعدادات
  await settingsService.loadSettings(); // تحميل جميع الإعدادات المحفوظة
  print('تم تحميل إعدادات التطبيق بنجاح'); // طباعة رسالة تأكيد التحميل

  // تهيئة خدمة الموقع وتطبيق إعدادات GPS
  // final locationService = LocationService(); // إنشاء مثيل من خدمة الموقع
  // await locationService.initialize(); // تهيئة خدمة الموقع وتطبيق الإعدادات
  // print('تم تهيئة خدمة الموقع بنجاح'); // طباعة رسالة تأكيد التهيئة

  runApp(MyApp()); // تشغيل التطبيق
}

// class MyApp extends StatelessWidget{
//   @override
//   Widget build(BuildContext context){
//     return MaterialApp(
//       debugShowCheckedModeBanner: false,
//       theme: ThemeData(
//         scaffoldBackgroundColor: Colors.white
//       ),
//       routes: {
//         "/":(context) => HomePage(),
//         "cartPage":(context) => CartPage()
//       },
//     );
//   }
// }

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
        ChangeNotifierProvider(create: (_) => CustomerDataProvider()),
        ChangeNotifierProvider(create: (_) => AuthenticationProvider()),
        ChangeNotifierProvider(create: (_) => SearchProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => AddressProvider()),
      ],
      child: ScreenUtilInit(
        designSize: Size(360, 690), // مقاس التصميم الأساسي
        builder: (context, child) {
          // تحميل البيانات عند بدء التطبيق
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Provider.of<CartProvider>(context, listen: false).loadCart();
            Provider.of<CustomerDataProvider>(context, listen: false)
                .loadCustomerData();
          });

          return MaterialApp(
            debugShowCheckedModeBanner: false,
            title: 'زاد اليمن',
            theme: ThemeData(
              scaffoldBackgroundColor: AppColors.whiteColor,
              primarySwatch: Colors.red,
              primaryColor: AppColors.primaryColor,
              fontFamily: 'Arial', // يمكن تغييرها لخط عربي
              // تحسين للأجهزة التي تحتوي على أزرار الملاحة في الشاشة
              bottomNavigationBarTheme: BottomNavigationBarThemeData(
                elevation: 8,
                backgroundColor: Colors.white,
              ),
              // تكوين الأزرار للتأكد من الحد الأدنى للمس
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(48, 48),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
              // تكوين الحقول
              inputDecorationTheme: InputDecorationTheme(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            builder: (context, widget) {
              // التأكد من أن النصوص لا تتأثر بشكل مفرط بإعدادات النظام
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: TextScaler.linear(
                    MediaQuery.of(context)
                        .textScaler
                        .scale(1.0)
                        .clamp(0.8, 1.3),
                  ),
                ),
                child: widget!,
              );
            },
            initialRoute: "/splash",
            routes: {
              "/splash": (context) => SplashScreen(),
              // "/": (context) => PhoneAuthScreen(),
              "/": (context) => HomePage(),
              "/home": (context) => HomePage(),
              "/cart": (context) => CartPage(),
              "cartPage": (context) => CartPage(),
              "/items": (context) => ItemsPages(),
              "itemsPage": (context) => ItemsPages(),
              "/orders": (context) => MyOrdersPage(),
              "/order-details": (context) => OrderDetailsPage(orderId: "001"),
              "/order-tracking": (context) => OrderTrackingPage(orderId: "001"),
              "/stores": (context) => StoresPage(),
              "/profile": (context) => ProfilePage(),
              "/settings": (context) => SettingsPage(),
              "/login": (context) => LoginPage(),
              "/register": (context) => RegisterPage(),
              "/favorites": (context) => FavoritesPage(),
              "/addresses": (context) => AddressesPage(),
              "/offers": (context) => OffersPage(),
            },
          );
        },
      ),
    );
  }
}
