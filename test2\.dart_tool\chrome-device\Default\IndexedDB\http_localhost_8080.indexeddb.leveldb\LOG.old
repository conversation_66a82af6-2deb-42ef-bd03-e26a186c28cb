2025/06/22-17:17:44.570 3834 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.a29c1c8\flutter_tools_chrome_device.4d1490ef\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/06/22-17:17:44.570 3834 Recovering log #30
2025/06/22-17:17:44.571 3834 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.a29c1c8\flutter_tools_chrome_device.4d1490ef\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000030.log 
2025/06/22-17:17:44.571 3834 Delete type=0 #4
2025/06/22-17:17:44.571 3834 Delete type=0 #7
2025/06/22-17:17:44.571 3834 Delete type=2 #8
2025/06/22-17:17:44.571 3834 Delete type=0 #10
2025/06/22-17:17:44.571 3834 Delete type=2 #12
2025/06/22-17:17:44.571 3834 Delete type=2 #16
2025/06/22-17:17:44.571 3834 Delete type=0 #18
2025/06/22-17:17:44.571 3834 Delete type=2 #20
2025/06/22-17:17:44.571 3834 Delete type=0 #22
2025/06/22-17:17:44.571 3834 Delete type=2 #24
2025/06/22-17:17:44.571 3834 Delete type=0 #26
2025/06/22-17:17:44.571 3834 Delete type=2 #28
2025/06/22-17:17:44.670 3834 Level-0 table #35: started
2025/06/22-17:17:44.672 3834 Level-0 table #35: 928 bytes OK
2025/06/22-17:17:44.680 3834 Delete type=0 #30
2025/06/22-17:17:44.680 3834 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 375 : 1
2025/06/22-17:17:44.680 3834 Compacting 1@0 + 1@1 files
2025/06/22-17:17:44.682 3834 Generated table #36@0: 50 keys, 1435 bytes
2025/06/22-17:17:44.682 3834 Compacted 1@0 + 1@1 files => 1435 bytes
2025/06/22-17:17:44.682 3834 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/22-17:17:44.682 3834 Delete type=2 #35
2025/06/22-17:17:44.683 3834 Manual compaction at level-0 from '\x00\x0b\x00\x00\x05' @ 375 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
