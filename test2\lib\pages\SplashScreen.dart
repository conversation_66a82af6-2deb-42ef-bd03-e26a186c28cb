import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/utils/AppConfig.dart';
import 'package:test2/providers/AuthenticationProvider.dart';
import 'package:test2/providers/CustomerDataProvider.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      duration: Duration(seconds: 3),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    // بدء الرسوم المتحركة
    _animationController.forward();

    // الانتقال إلى الصفحة المناسبة بعد المدة المحددة
    Future.delayed(Duration(seconds: AppConfig.splashDuration), () {
      _checkUserAndNavigate();
    });
  }

  // التحقق من المستخدم والانتقال للصفحة المناسبة
  Future<void> _checkUserAndNavigate() async {
    try {
      // التأكد من أن الـ context ما زال صالحاً
      if (!mounted) return;

      // الحصول على مزودي الحالة
      final authProvider = Provider.of<AuthenticationProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerDataProvider>(context, listen: false);

      // تهيئة نظام المصادقة
      await authProvider.initializeAuth();

      // تحميل بيانات العميل
      await customerProvider.loadCustomerData();

      // التأكد من أن الـ context ما زال صالحاً قبل التنقل
      if (!mounted) return;

      // الذهاب مباشرة للصفحة الرئيسية
      // سيتم التحقق من المصادقة عند الحاجة (إتمام الطلب أو البروفايل)
      Navigator.pushReplacementNamed(context, '/');
    } catch (e) {
      print('خطأ في SplashScreen: $e');
      // التأكد من أن الـ context ما زال صالحاً قبل التنقل
      if (!mounted) return;

      // في حالة الخطأ، اذهب للصفحة الرئيسية
      Navigator.pushReplacementNamed(context, '/');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primaryColor,
              AppColors.primaryColor.withOpacity(0.8),
              AppColors.accentColor.withOpacity(0.3),
            ],
          ),
        ),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // شعار التطبيق
                    Container(
                      width: 150.w,
                      height: 150.h,
                      decoration: BoxDecoration(
                        color: AppColors.whiteColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadowColor,
                            blurRadius: 20,
                            spreadRadius: 5,
                            offset: Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Icon(
                          Icons.restaurant_menu,
                          size: 80.sp,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),

                    SizedBox(height: 30.h),

                    // اسم التطبيق
                    Text(
                      AppConfig.appName,
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.whiteColor,
                        letterSpacing: 2,
                      ),
                    ),

                    SizedBox(height: 10.h),

                    // شعار التطبيق
                    Text(
                      AppConfig.appDescription,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.whiteColor.withOpacity(0.9),
                        fontWeight: FontWeight.w300,
                      ),
                    ),

                    SizedBox(height: 50.h),

                    // مؤشر التحميل
                    Container(
                      width: 50.w,
                      height: 50.h,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.whiteColor,
                        ),
                        strokeWidth: 3,
                      ),
                    ),

                    SizedBox(height: 20.h),

                    Text(
                      "جاري التحميل...",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.whiteColor.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
