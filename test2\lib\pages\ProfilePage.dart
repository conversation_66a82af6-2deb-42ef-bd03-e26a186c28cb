import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/widgets/ProfileAppBar.dart';
import 'package:test2/pages/NotificationSettingsPage.dart';
import 'package:test2/pages/PaymentMethodsPage.dart';
import 'package:test2/pages/NewUserRegistrationPage.dart';
import 'package:test2/pages/WelcomePage.dart';
import 'package:test2/providers/CustomerDataProvider.dart';

class ProfilePage extends StatefulWidget {
  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  void initState() {
    super.initState();
    _loadCustomerData();
  }

  void _loadCustomerData() async {
    final customerProvider =
        Provider.of<CustomerDataProvider>(context, listen: false);
    await customerProvider.ensureDataLoaded();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: ProfileAppBar(),
      body: Column(
        children: [
          // الجزء الرئيسي - متجاوب
          Expanded(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: AppDimensions.paddingMedium),
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
                  topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // معلومات المستخدم الأساسية
                    _buildUserInfo(),

                    SizedBox(height: 20.h),

                    // قائمة الخيارات - متجاوبة
                    _buildMenuOptions(context),

                    SizedBox(height: 20.h),

                    // إعدادات التطبيق - متجاوبة
                    _buildAppSettings(context),

                    SizedBox(height: 30.h),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Consumer<CustomerDataProvider>(
      builder: (context, customerProvider, child) {
        final isLoggedIn = customerProvider.isLoggedIn;
        final fullName = customerProvider.fullName;
        final phone = customerProvider.phone;
        final email = customerProvider.email;

        return Container(
          margin: EdgeInsets.symmetric(horizontal: AppDimensions.marginLarge),
          padding: EdgeInsets.all(AppDimensions.paddingLarge),
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius:
                BorderRadius.circular(AppDimensions.borderRadiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowColor,
                blurRadius: 10,
                spreadRadius: 2,
                offset: Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              // صورة المستخدم
              Container(
                width: 80.w,
                height: 80.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.backgroundColor,
                  border: Border.all(
                    color: AppColors.primaryColor,
                    width: 3,
                  ),
                ),
                child: Icon(
                  Icons.person,
                  size: 40.sp,
                  color: AppColors.primaryColor,
                ),
              ),

              SizedBox(width: 15.w),

              // معلومات المستخدم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  textDirection: TextDirection.rtl,
                  children: [
                    Text(
                      isLoggedIn && fullName.isNotEmpty ? fullName : 'غير مسجل',
                      style: AppTextStyles.titleMedium,
                    ),
                    SizedBox(height: 5.h),
                    if (isLoggedIn && phone.isNotEmpty) ...[
                      Text(
                        phone,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 5.h),
                    ],
                    if (isLoggedIn && email.isNotEmpty) ...[
                      Text(
                        email,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 5.h),
                    ],
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: !isLoggedIn
                            ? Colors.orange.withOpacity(0.1)
                            : AppColors.successColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        isLoggedIn ? 'مسجل' : 'غير مسجل',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: !isLoggedIn
                              ? Colors.orange
                              : AppColors.successColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // أيقونة التعديل أو التسجيل
              IconButton(
                onPressed: () {
                  if (!isLoggedIn) {
                    _showRegistrationDialog();
                  } else {
                    // TODO: إضافة صفحة تعديل الملف الشخصي
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('صفحة تعديل الملف الشخصي قريباً')),
                    );
                  }
                },
                icon: Icon(
                  !isLoggedIn ? Icons.person_add : Icons.edit,
                  color: AppColors.primaryColor,
                  size: AppDimensions.iconSizeMedium,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMenuOptions(BuildContext context) {
    List<Map<String, dynamic>> menuItems = [
      {
        "icon": Icons.shopping_bag_outlined,
        "title": "طلباتي",
        "subtitle": "عرض جميع الطلبات السابقة",
        "onTap": () => Navigator.pushNamed(context, "/orders"),
      },
      {
        "icon": Icons.favorite_outline,
        "title": "المفضلة",
        "subtitle": "المطاعم والأطباق المفضلة",
        "onTap": () => Navigator.pushNamed(context, "/favorites"),
      },
      {
        "icon": Icons.location_on_outlined,
        "title": "عناويني",
        "subtitle": "إدارة عناوين التوصيل",
        "onTap": () => Navigator.pushNamed(context, "/addresses"),
      },
      {
        "icon": Icons.payment_outlined,
        "title": "طرق الدفع",
        "subtitle": "إدارة طرق الدفع",
        "onTap": () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentMethodsPage(),
            ),
          );
        },
      },
      {
        "icon": Icons.notifications_outlined,
        "title": "الإشعارات",
        "subtitle": "إعدادات الإشعارات",
        "onTap": () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NotificationSettingsPage(),
            ),
          );
        },
      },
      {
        "icon": Icons.settings_outlined,
        "title": "الإعدادات",
        "subtitle": "إعدادات التطبيق العامة",
        "onTap": () => Navigator.pushNamed(context, "/settings"),
      },
    ];

    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.marginLarge),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: menuItems.map((item) {
          return _buildMenuItem(
            icon: item["icon"],
            title: item["title"],
            subtitle: item["subtitle"],
            onTap: item["onTap"],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingMedium),
        child: Row(
          textDirection: TextDirection.rtl,
          children: [
            Container(
              width: 50.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius:
                    BorderRadius.circular(AppDimensions.borderRadiusSmall),
              ),
              child: Icon(
                icon,
                color: AppColors.primaryColor,
                size: AppDimensions.iconSizeMedium,
              ),
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection: TextDirection.rtl,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 3.h),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall,
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_back_ios,
              color: AppColors.textSecondaryColor,
              size: 18.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettings(BuildContext context) {
    return Consumer<CustomerDataProvider>(
      builder: (context, customerProvider, child) {
        final isLoggedIn = customerProvider.isLoggedIn;

        return Container(
          margin: EdgeInsets.symmetric(horizontal: AppDimensions.marginLarge),
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius:
                BorderRadius.circular(AppDimensions.borderRadiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowColor,
                blurRadius: 10,
                spreadRadius: 2,
                offset: Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildMenuItem(
                icon: Icons.help_outline,
                title: "المساعدة والدعم",
                subtitle: "الأسئلة الشائعة وخدمة العملاء",
                onTap: () {},
              ),
              _buildMenuItem(
                icon: Icons.info_outline,
                title: "حول التطبيق",
                subtitle: "معلومات عن زاد اليمن",
                onTap: () {},
              ),
              _buildMenuItem(
                icon: !isLoggedIn ? Icons.login : Icons.logout,
                title: !isLoggedIn ? "تسجيل دخول" : "تسجيل الخروج",
                subtitle: !isLoggedIn
                    ? "إنشاء حساب أو تسجيل دخول"
                    : "الخروج من الحساب",
                onTap: () {
                  if (!isLoggedIn) {
                    _showRegistrationDialog();
                  } else {
                    _showLogoutDialog(context, customerProvider);
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLogoutDialog(
      BuildContext context, CustomerDataProvider customerProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "تسجيل الخروج",
            textAlign: TextAlign.right,
            style: AppTextStyles.titleMedium,
          ),
          content: Text(
            "هل أنت متأكد من رغبتك في تسجيل الخروج؟",
            textAlign: TextAlign.right,
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "إلغاء",
                style: TextStyle(color: AppColors.textSecondaryColor),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                // تنفيذ عملية تسجيل الخروج
                await customerProvider.logout();
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => WelcomePage()),
                );
              },
              child: Text(
                "تسجيل الخروج",
                style: TextStyle(color: AppColors.primaryColor),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showRegistrationDialog() {
    // التوجه مباشرة لصفحة تسجيل الحساب
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewUserRegistrationPage(),
      ),
    );
  }
}
