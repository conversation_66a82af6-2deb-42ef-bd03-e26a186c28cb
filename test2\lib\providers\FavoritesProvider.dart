import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/Store.dart';
import '../models/Product.dart';
import '../services/FirebaseProductService.dart';

/// مزود المفضلات - يدير المحلات والمنتجات المفضلة
class FavoritesProvider with ChangeNotifier {
  // قوائم المفضلات
  List<String> _favoriteStoreIds = [];
  List<String> _favoriteProductIds = [];
  
  // كاش للبيانات المفضلة
  List<Store> _favoriteStores = [];
  List<Product> _favoriteProducts = [];
  
  // حالة التحميل
  bool _isLoading = false;
  String? _error;

  // مفاتيح التخزين المحلي
  static const String _storesFavoritesKey = 'favorite_stores';
  static const String _productsFavoritesKey = 'favorite_products';

  // Getters
  List<String> get favoriteStoreIds => List.unmodifiable(_favoriteStoreIds);
  List<String> get favoriteProductIds => List.unmodifiable(_favoriteProductIds);
  List<Store> get favoriteStores => List.unmodifiable(_favoriteStores);
  List<Product> get favoriteProducts => List.unmodifiable(_favoriteProducts);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// تحميل المفضلات من التخزين المحلي
  Future<void> loadFavorites() async {
    try {
      _setLoading(true);
      _clearError();

      final prefs = await SharedPreferences.getInstance();
      
      // تحميل معرفات المحلات المفضلة
      final storeIds = prefs.getStringList(_storesFavoritesKey) ?? [];
      _favoriteStoreIds = storeIds;
      
      // تحميل معرفات المنتجات المفضلة
      final productIds = prefs.getStringList(_productsFavoritesKey) ?? [];
      _favoriteProductIds = productIds;

      print('تم تحميل المفضلات: ${_favoriteStoreIds.length} محل، ${_favoriteProductIds.length} منتج');
      
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المفضلات: ${e.toString()}');
      print('خطأ في تحميل المفضلات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// حفظ المفضلات في التخزين المحلي
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ معرفات المحلات المفضلة
      await prefs.setStringList(_storesFavoritesKey, _favoriteStoreIds);
      
      // حفظ معرفات المنتجات المفضلة
      await prefs.setStringList(_productsFavoritesKey, _favoriteProductIds);
      
      print('تم حفظ المفضلات بنجاح');
    } catch (e) {
      print('خطأ في حفظ المفضلات: $e');
      throw Exception('فشل في حفظ المفضلات');
    }
  }

  /// إضافة/إزالة محل من المفضلة
  Future<bool> toggleStoreFavorite(String storeId) async {
    try {
      if (_favoriteStoreIds.contains(storeId)) {
        // إزالة من المفضلة
        _favoriteStoreIds.remove(storeId);
        _favoriteStores.removeWhere((store) => store.id == storeId);
        print('تم إزالة المحل $storeId من المفضلة');
      } else {
        // إضافة للمفضلة
        _favoriteStoreIds.add(storeId);
        print('تم إضافة المحل $storeId للمفضلة');
      }
      
      await _saveFavorites();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في تحديث مفضلة المحل: ${e.toString()}');
      print('خطأ في تحديث مفضلة المحل: $e');
      return false;
    }
  }

  /// إضافة/إزالة منتج من المفضلة
  Future<bool> toggleProductFavorite(String productId) async {
    try {
      if (_favoriteProductIds.contains(productId)) {
        // إزالة من المفضلة
        _favoriteProductIds.remove(productId);
        _favoriteProducts.removeWhere((product) => product.id == productId);
        print('تم إزالة المنتج $productId من المفضلة');
      } else {
        // إضافة للمفضلة
        _favoriteProductIds.add(productId);
        print('تم إضافة المنتج $productId للمفضلة');
      }
      
      await _saveFavorites();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في تحديث مفضلة المنتج: ${e.toString()}');
      print('خطأ في تحديث مفضلة المنتج: $e');
      return false;
    }
  }

  /// التحقق من كون المحل مفضل
  bool isStoreFavorite(String storeId) {
    return _favoriteStoreIds.contains(storeId);
  }

  /// التحقق من كون المنتج مفضل
  bool isProductFavorite(String productId) {
    return _favoriteProductIds.contains(productId);
  }

  /// التحقق من كون المنتج مفضل (اسم بديل)
  bool isFavorite(String productId) {
    return isProductFavorite(productId);
  }

  /// إضافة منتج للمفضلة
  Future<void> addToFavorites(String productId) async {
    if (!_favoriteProductIds.contains(productId)) {
      await toggleProductFavorite(productId);
    }
  }

  /// إزالة منتج من المفضلة
  Future<void> removeFromFavorites(String productId) async {
    if (_favoriteProductIds.contains(productId)) {
      await toggleProductFavorite(productId);
    }
  }

  /// تحديث كاش المحلات المفضلة
  void updateFavoriteStores(List<Store> allStores) {
    _favoriteStores = allStores
        .where((store) => _favoriteStoreIds.contains(store.id))
        .toList();
    notifyListeners();
  }

  /// تحديث كاش المنتجات المفضلة
  void updateFavoriteProducts(List<Product> allProducts) {
    _favoriteProducts = allProducts
        .where((product) => _favoriteProductIds.contains(product.id))
        .toList();
    notifyListeners();
  }

  /// تحديث المنتجات المفضلة من Firebase
  Future<void> refreshFavoriteProducts() async {
    if (_favoriteProductIds.isEmpty) {
      _favoriteProducts.clear();
      notifyListeners();
      return;
    }

    try {
      _setLoading(true);
      final FirebaseProductService productService = FirebaseProductService();
      final List<Product> products = [];

      // جلب كل منتج مفضل من Firebase
      for (String productId in _favoriteProductIds) {
        final product = await productService.getProductById(productId);
        if (product != null) {
          products.add(product);
        }
      }

      _favoriteProducts = products;
      print('تم تحديث ${_favoriteProducts.length} منتج مفضل من Firebase');
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحديث المنتجات المفضلة: ${e.toString()}');
      print('خطأ في تحديث المنتجات المفضلة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// مسح جميع المفضلات
  Future<bool> clearAllFavorites() async {
    try {
      _favoriteStoreIds.clear();
      _favoriteProductIds.clear();
      _favoriteStores.clear();
      _favoriteProducts.clear();
      
      await _saveFavorites();
      notifyListeners();
      
      print('تم مسح جميع المفضلات');
      return true;
    } catch (e) {
      _setError('فشل في مسح المفضلات: ${e.toString()}');
      print('خطأ في مسح المفضلات: $e');
      return false;
    }
  }

  /// الحصول على عدد المفضلات
  int get totalFavoritesCount => _favoriteStoreIds.length + _favoriteProductIds.length;
  int get favoriteStoresCount => _favoriteStoreIds.length;
  int get favoriteProductsCount => _favoriteProductIds.length;

  /// البحث في المفضلات
  List<Store> searchFavoriteStores(String query) {
    if (query.isEmpty) return _favoriteStores;
    
    return _favoriteStores.where((store) {
      return store.name.toLowerCase().contains(query.toLowerCase()) ||
             store.category.toLowerCase().contains(query.toLowerCase()) ||
             store.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  List<Product> searchFavoriteProducts(String query) {
    if (query.isEmpty) return _favoriteProducts;
    
    return _favoriteProducts.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
             product.description.toLowerCase().contains(query.toLowerCase()) ||
             product.category.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// فلترة المفضلات حسب الفئة
  List<Store> getFavoriteStoresByCategory(String category) {
    if (category == 'الكل') return _favoriteStores;
    
    return _favoriteStores.where((store) => store.category == category).toList();
  }

  List<Product> getFavoriteProductsByCategory(String category) {
    if (category == 'الكل') return _favoriteProducts;
    
    return _favoriteProducts.where((product) => product.category == category).toList();
  }

  /// الحصول على إحصائيات المفضلات
  Map<String, int> get favoritesStats {
    final storeCategories = <String, int>{};
    final productCategories = <String, int>{};
    
    for (var store in _favoriteStores) {
      storeCategories[store.category] = (storeCategories[store.category] ?? 0) + 1;
    }
    
    for (var product in _favoriteProducts) {
      productCategories[product.category] = (productCategories[product.category] ?? 0) + 1;
    }
    
    return {
      'totalStores': _favoriteStores.length,
      'totalProducts': _favoriteProducts.length,
      'storeCategories': storeCategories.length,
      'productCategories': productCategories.length,
    };
  }

  // دوال مساعدة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// تصدير المفضلات (للنسخ الاحتياطي)
  Map<String, dynamic> exportFavorites() {
    return {
      'favoriteStores': _favoriteStoreIds,
      'favoriteProducts': _favoriteProductIds,
      'exportDate': DateTime.now().toIso8601String(),
      'version': '1.0',
    };
  }

  /// استيراد المفضلات (من النسخ الاحتياطي)
  Future<bool> importFavorites(Map<String, dynamic> data) async {
    try {
      final storeIds = List<String>.from(data['favoriteStores'] ?? []);
      final productIds = List<String>.from(data['favoriteProducts'] ?? []);
      
      _favoriteStoreIds = storeIds;
      _favoriteProductIds = productIds;
      
      await _saveFavorites();
      notifyListeners();
      
      print('تم استيراد المفضلات بنجاح');
      return true;
    } catch (e) {
      _setError('فشل في استيراد المفضلات: ${e.toString()}');
      print('خطأ في استيراد المفضلات: $e');
      return false;
    }
  }
}
