import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/AuthenticationProvider.dart';
import '../pages/InitialLoginPage.dart';

/// حارس المصادقة - يتحكم في الوصول للصفحات
class AuthGuard extends StatefulWidget {
  final Widget child;
  final bool requireAuth;
  final String? redirectMessage;

  const AuthGuard({
    Key? key,
    required this.child,
    this.requireAuth = false,
    this.redirectMessage,
  }) : super(key: key);

  @override
  _AuthGuardState createState() => _AuthGuardState();
}

class _AuthGuardState extends State<AuthGuard> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    // تأجيل التنفيذ لتجنب استدعاء notifyListeners أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      final authProvider = Provider.of<AuthenticationProvider>(context, listen: false);
      await authProvider.initializeAuth();

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildLoadingScreen();
    }

    return Consumer<AuthenticationProvider>(
      builder: (context, authProvider, child) {
        // إذا كان يحتاج مصادقة ولم يكن مصادق
        if (widget.requireAuth && authProvider.needsAuthentication) {
          return InitialLoginPage();
        }

        // إذا كان مصادق أو لا يحتاج مصادقة
        return widget.child;
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // شعار التطبيق
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Color(0xFF4C53A5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.restaurant_menu,
                color: Colors.white,
                size: 50,
              ),
            ),
            
            SizedBox(height: 30),
            
            // اسم التطبيق
            Text(
              'زاد',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
            
            SizedBox(height: 10),
            
            Text(
              'أفضل تطبيق لطلب الطعام',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            
            SizedBox(height: 40),
            
            // مؤشر التحميل
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4C53A5)),
            ),
            
            SizedBox(height: 20),
            
            Text(
              'جاري التحميل...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// دالة مساعدة للتحقق من المصادقة قبل الانتقال
class AuthHelper {
  /// التحقق من المصادقة قبل تنفيذ عملية
  static Future<bool> checkAuthBeforeAction(
    BuildContext context, {
    String? message,
  }) async {
    final authProvider = Provider.of<AuthenticationProvider>(context, listen: false);
    
    // التحقق من حالة المصادقة
    if (authProvider.needsAuthentication) {
      // عرض رسالة وتوجيه لتسجيل الدخول
      _showAuthRequiredDialog(context, message);
      return false;
    }
    
    // تحديث آخر نشاط
    await authProvider.updateLastActivity();
    return true;
  }

  /// عرض حوار يطلب تسجيل الدخول
  static void _showAuthRequiredDialog(BuildContext context, String? message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        title: Row(
          children: [
            Icon(Icons.lock_outline, color: Color(0xFF4C53A5)),
            SizedBox(width: 10),
            Text(
              'تسجيل الدخول مطلوب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
          ],
        ),
        content: Text(
          message ?? 'يرجى إعداد حسابك أولاً للمتابعة',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => InitialLoginPage()),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'إعداد الحساب',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// التحقق من المصادقة مع إعادة توجيه تلقائية
  static Future<void> requireAuthWithRedirect(
    BuildContext context, {
    String? message,
  }) async {
    final authProvider = Provider.of<AuthenticationProvider>(context, listen: false);
    
    if (authProvider.needsAuthentication) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => InitialLoginPage()),
      );
    }
  }
}

/// Widget للصفحات التي تتطلب مصادقة
class RequireAuth extends StatelessWidget {
  final Widget child;
  final String? message;

  const RequireAuth({
    Key? key,
    required this.child,
    this.message,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AuthGuard(
      requireAuth: true,
      redirectMessage: message,
      child: child,
    );
  }
}

/// Widget للصفحات التي لا تتطلب مصادقة
class OptionalAuth extends StatelessWidget {
  final Widget child;

  const OptionalAuth({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AuthGuard(
      requireAuth: false,
      child: child,
    );
  }
}
