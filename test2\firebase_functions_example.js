// مثال على Firebase Cloud Functions لإرسال الإشعارات
// هذا الملف للمرجع فقط - يجب رفعه إلى Firebase Functions

const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// دالة لإرسال إشعار عند تحديث حالة الطلب
exports.sendOrderStatusNotification = functions.firestore
  .document('orders/{orderId}')
  .onUpdate(async (change, context) => {
    const orderId = context.params.orderId;
    const beforeData = change.before.data();
    const afterData = change.after.data();
    
    // التحقق من تغيير الحالة
    if (beforeData.status !== afterData.status) {
      const userId = afterData.userId;
      const storeName = afterData.storeName || 'المتجر';
      const newStatus = afterData.status;
      
      try {
        // الحصول على FCM Token للمستخدم
        const userDoc = await admin.firestore()
          .collection('users')
          .doc(userId)
          .get();
          
        if (!userDoc.exists) {
          console.log('المستخدم غير موجود:', userId);
          return;
        }
        
        const userData = userDoc.data();
        const fcmToken = userData.fcmToken;
        
        if (!fcmToken) {
          console.log('لا يوجد FCM Token للمستخدم:', userId);
          return;
        }
        
        // إنشاء رسالة الإشعار
        const notificationMessage = getStatusMessage(newStatus, storeName);
        const notificationIcon = getStatusIcon(newStatus);
        
        const message = {
          token: fcmToken,
          notification: {
            title: `${notificationIcon} تحديث طلبك #${orderId.substring(orderId.length - 6)}`,
            body: notificationMessage,
          },
          data: {
            type: getNotificationType(newStatus),
            orderId: orderId,
            status: newStatus,
            storeName: storeName,
            action: 'order_status_update',
            timestamp: new Date().toISOString(),
          },
          android: {
            notification: {
              channelId: 'orders_channel',
              priority: 'high',
              defaultSound: true,
              defaultVibrateTimings: true,
            },
          },
          apns: {
            payload: {
              aps: {
                sound: 'default',
                badge: 1,
              },
            },
          },
        };
        
        // إرسال الإشعار
        const response = await admin.messaging().send(message);
        console.log('تم إرسال الإشعار بنجاح:', response);
        
        // حفظ الإشعار في قاعدة البيانات للمراجع
        await admin.firestore()
          .collection('notifications')
          .add({
            userId: userId,
            orderId: orderId,
            type: getNotificationType(newStatus),
            title: message.notification.title,
            body: message.notification.body,
            data: message.data,
            sentAt: admin.firestore.FieldValue.serverTimestamp(),
            status: 'sent',
          });
          
      } catch (error) {
        console.error('خطأ في إرسال الإشعار:', error);
        
        // حفظ الإشعار الفاشل للمحاولة لاحقاً
        await admin.firestore()
          .collection('failed_notifications')
          .add({
            userId: userId,
            orderId: orderId,
            status: newStatus,
            error: error.message,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          });
      }
    }
  });

// دالة لإعادة محاولة إرسال الإشعارات الفاشلة
exports.retryFailedNotifications = functions.pubsub
  .schedule('every 5 minutes')
  .onRun(async (context) => {
    const failedNotifications = await admin.firestore()
      .collection('failed_notifications')
      .where('retryCount', '<', 3)
      .limit(10)
      .get();
      
    const batch = admin.firestore().batch();
    
    for (const doc of failedNotifications.docs) {
      const data = doc.data();
      
      try {
        // محاولة إرسال الإشعار مرة أخرى
        const userDoc = await admin.firestore()
          .collection('users')
          .doc(data.userId)
          .get();
          
        if (userDoc.exists && userDoc.data().fcmToken) {
          const message = {
            token: userDoc.data().fcmToken,
            notification: {
              title: `تحديث طلبك #${data.orderId.substring(data.orderId.length - 6)}`,
              body: getStatusMessage(data.status, 'المتجر'),
            },
            data: {
              type: getNotificationType(data.status),
              orderId: data.orderId,
              status: data.status,
              action: 'order_status_update',
            },
          };
          
          await admin.messaging().send(message);
          
          // حذف الإشعار الفاشل بعد النجاح
          batch.delete(doc.ref);
        }
      } catch (error) {
        // زيادة عداد المحاولات
        batch.update(doc.ref, {
          retryCount: admin.firestore.FieldValue.increment(1),
          lastRetry: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    }
    
    await batch.commit();
  });

// دوال مساعدة
function getStatusMessage(status, storeName) {
  switch (status) {
    case 'confirmed':
      return `تم تأكيد طلبك من ${storeName} وبدء التجهيز`;
    case 'preparing':
      return `يتم تجهيز طلبك الآن في ${storeName}`;
    case 'ready':
      return 'طلبك جاهز وفي انتظار عامل التوصيل';
    case 'pickedUp':
      return 'تم استلام طلبك من عامل التوصيل';
    case 'onTheWay':
      return 'عامل التوصيل في الطريق إليك';
    case 'delivered':
      return 'تم توصيل طلبك بنجاح! نتمنى أن تكون راضياً عن الخدمة';
    case 'cancelled':
      return 'تم إلغاء طلبك. سيتم استرداد المبلغ خلال 3-5 أيام عمل';
    default:
      return 'تم تحديث حالة طلبك';
  }
}

function getStatusIcon(status) {
  switch (status) {
    case 'confirmed': return '✅';
    case 'preparing': return '👨‍🍳';
    case 'ready': return '📦';
    case 'pickedUp': return '🚗';
    case 'onTheWay': return '🛣️';
    case 'delivered': return '🎉';
    case 'cancelled': return '❌';
    default: return '📋';
  }
}

function getNotificationType(status) {
  switch (status) {
    case 'confirmed': return 'order_confirmed';
    case 'preparing': return 'order_preparing';
    case 'ready': return 'order_ready';
    case 'delivered': return 'order_delivered';
    default: return 'general';
  }
}

// دالة لتنظيف الإشعارات القديمة
exports.cleanupOldNotifications = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const oldNotifications = await admin.firestore()
      .collection('notifications')
      .where('sentAt', '<', thirtyDaysAgo)
      .get();
      
    const batch = admin.firestore().batch();
    
    oldNotifications.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`تم حذف ${oldNotifications.size} إشعار قديم`);
  });
